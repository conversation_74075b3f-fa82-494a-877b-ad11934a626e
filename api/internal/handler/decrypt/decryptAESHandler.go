package decrypt

import (
	"net/http"

	"engine/api/internal/logic/decrypt"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"engine/common/xerr"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func DecryptAESHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AESDecryptReq
		if err := httpx.Parse(r, &req); err != nil {
			// 参数错误也不暴露具体信息
			result.HttpResult(r, w, nil, xerr.NewErrMsg("操作失败"))
			return
		}

		l := decrypt.NewDecryptAESLogic(r.Context(), svcCtx)
		resp, err := l.DecryptAES(&req)
		if err != nil {
			// 解密失败，返回通用错误信息
			result.HttpResult(r, w, nil, err)
			return
		}

		// 解密成功，重定向到目标URL
		if resp != nil && resp.DecryptedData != "" {
			http.Redirect(w, r, resp.DecryptedData, http.StatusFound)
			return
		}

		// 如果解密结果为空，返回错误
		result.HttpResult(r, w, nil, xerr.NewErrMsg("操作失败"))
	}
}
