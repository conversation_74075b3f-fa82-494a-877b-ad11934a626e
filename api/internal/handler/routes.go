// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"
	"time"

	decrypt "engine/api/internal/handler/decrypt"
	merchant "engine/api/internal/handler/merchant"
	notify "engine/api/internal/handler/notify"
	prize "engine/api/internal/handler/prize"
	redemptions "engine/api/internal/handler/redemptions"
	user "engine/api/internal/handler/user"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/checkSecret",
					Handler: decrypt.DecryptAESHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/decrypt"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/decryptData",
					Handler: decrypt.DecryptRSAHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/decrypt"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.MerchantAuth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/prizeRedemption",
					Handler: decrypt.PrizeRedemptionHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/oneclicklogin",
					Handler: merchant.WeChatAppletPhoneLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant/miniprogram"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.MerchantAuth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/binding",
					Handler: merchant.BindingHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/pointsSummary",
					Handler: merchant.PointsSummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/redeemRecords",
					Handler: merchant.RedeemRecordsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/redemption",
					Handler: merchant.RedemptionHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/redemptionRecords",
					Handler: merchant.RedemptionRecordsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant/miniprogram"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/change",
					Handler: merchant.ChangeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/merchantList",
					Handler: merchant.MerchantListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant/admin"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/shipment",
					Handler: notify.ShipmentNotifyHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/redemptions/notify"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/myPrizeRecordList",
					Handler: prize.MyPrizeRecordListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/prizeRecordSummary",
					Handler: prize.PrizeRecordSummaryHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/prize"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/batchAdd",
					Handler: prize.BatchAddHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/batchChange",
					Handler: prize.BatchChangeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/batchList",
					Handler: prize.BatchListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/batchRegionConfUpdare",
					Handler: prize.BatchRegionConfUpdareHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/batchUpdate",
					Handler: prize.BatchUpdateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/generateCards",
					Handler: prize.GenerateCardsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/prizeConfAdd",
					Handler: prize.PrizeConfAddHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/prizeConfDetail",
					Handler: prize.PrizeConfDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/prizeConfUpdare",
					Handler: prize.PrizeConfUpdareHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/prizeRecordList",
					Handler: prize.PrizeRecordListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/prize/admin"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/redemptionList",
					Handler: redemptions.RedemptionListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/redemptions/admin"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/oneclicklogin",
					Handler: user.WeChatAppletPhoneLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/user"),
	)
}
