package merchant

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/merchant"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func RedemptionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RedemptionReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := merchant.NewRedemptionLogic(r.Context(), svcCtx)
		err := l.Redemption(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
