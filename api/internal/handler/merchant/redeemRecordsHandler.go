package merchant

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/merchant"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func RedeemRecordsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RedeemRecordsReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := merchant.NewRedeemRecordsLogic(r.Context(), svcCtx)
		resp, err := l.RedeemRecords(&req)
		result.HttpResult(r, w, resp, err)
	}
}
