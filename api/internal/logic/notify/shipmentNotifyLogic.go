package notify

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"errors"
	"github.com/spf13/cast"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShipmentNotifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShipmentNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShipmentNotifyLogic {
	return &ShipmentNotifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShipmentNotifyLogic) ShipmentNotify(req *types.ShipmentNotifyReq) error {
	//截取mulandoredeem后面的字符串为id
	if !strings.HasPrefix(req.OrderNo, "mulandoredeem") {
		return nil
	}

	id := cast.ToUint64(req.OrderNo[13:])

	redemption, err := l.svcCtx.MerchantRedemptionModel.FindOne(l.ctx, id)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("ShipmentNotify MerchantRedemptionModel.FindOne err %v", err)
		return nil
	}

	if redemption == nil || redemption.Status != 1 {
		return nil
	}

	redemption.Status = 2
	redemption.ShippingCode = req.WayBill
	redemption.ShipTime = time.Now()

	err = l.svcCtx.MerchantRedemptionModel.Update(l.ctx, redemption)
	if err != nil {
		l.Errorf("ShipmentNotify MerchantRedemptionModel.Update err %v", err)
	}

	return nil
}
