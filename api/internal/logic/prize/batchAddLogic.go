package prize

import (
	"context"
	"engine/api/internal/logic"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchAddLogic {
	return &BatchAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchAddLogic) BatchAdd(req *types.BatchAddReq) error {
	// 解析开始时间和结束时间
	startTime, err := common.ParseTime(req.StartTime)
	if err != nil {
		l.Errorf("BatchAdd.ParseTime startTime err: %v", err)
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "开始时间格式错误")
	}

	endTime, err := common.ParseTime(req.EndTime)
	if err != nil {
		l.Errorf("BatchAdd.ParseTime endTime err: %v", err)
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "结束时间格式错误")
	}

	// 验证时间逻辑
	if !endTime.After(startTime) {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "结束时间必须大于开始时间")
	}

	// 创建批次数据
	batch := &model.VhBatch{
		Title:     req.Title,
		ShortCode: req.ShortCode,
		GoodsName: req.GoodsName,
		Desc:      req.Desc,
		Status:    req.Status, // 默认启用状态
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 插入数据库
	row, err := l.svcCtx.BatchModel.Insert(l.ctx, batch)
	if err != nil {
		l.Errorf("BatchAdd.BatchModel.Insert err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	batchId, err := row.LastInsertId()
	if err != nil {
		l.Errorf("BatchAdd.BatchModel.Insert.LastInsertId err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	//每个大区新增默认奖品
	var datas []*model.VhPrizeConf
	regions := logic.GetAllRegions()
	for _, region := range regions {
		//默认增加 再来一瓶、5折购酒、7折购酒、9折购酒
		datas = append(datas, &model.VhPrizeConf{
			BatchId:        uint64(batchId),
			RegionCode:     region.Code,
			RegionName:     region.Name,
			PrizeName:      "再来一瓶",
			Status:         1,
			Points:         100,
			RedeemQuantity: 0,
			PrizeCt:        0,
			WinCt:          0,
			ValidDays:      30,
		}, &model.VhPrizeConf{
			BatchId:        uint64(batchId),
			RegionCode:     region.Code,
			RegionName:     region.Name,
			PrizeName:      "5折购酒",
			Status:         1,
			Points:         50,
			RedeemQuantity: 0,
			PrizeCt:        0,
			WinCt:          0,
			ValidDays:      30,
		}, &model.VhPrizeConf{
			BatchId:        uint64(batchId),
			RegionCode:     region.Code,
			RegionName:     region.Name,
			PrizeName:      "7折购酒",
			Status:         1,
			Points:         30,
			RedeemQuantity: 0,
			PrizeCt:        0,
			WinCt:          0,
			ValidDays:      30,
		}, &model.VhPrizeConf{
			BatchId:        uint64(batchId),
			RegionCode:     region.Code,
			RegionName:     region.Name,
			PrizeName:      "9折购酒",
			Status:         1,
			Points:         10,
			RedeemQuantity: 0,
			PrizeCt:        0,
			WinCt:          0,
			ValidDays:      30,
		})
	}

	_, err = l.svcCtx.PrizeConfModel.Inserts(l.ctx, datas)
	if err != nil {
		l.Errorf("BatchAdd.PrizeConfModel.Inserts err: %v", err)
	}

	return nil
}
