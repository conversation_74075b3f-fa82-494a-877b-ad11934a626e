package prize

import (
	"context"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeRecordSummaryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeRecordSummaryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeRecordSummaryLogic {
	return &PrizeRecordSummaryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type RecordSummary struct {
	TotalWins       uint64 `db:"total_wins"`
	RedemptionCount uint64 `db:"redemption_count"`
}

func (l *PrizeRecordSummaryLogic) PrizeRecordSummary() (resp *types.PrizeRecordSummaryResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	var recordSummary RecordSummary
	builder := squirrel.Select("count(*) as total_wins,sum(case when status = 2 then 1 else 0 end) as redemption_count").
		From(l.svcCtx.PrizeRecordModel.TableName()).Where(squirrel.Eq{"uid": uid})
	err = l.svcCtx.PrizeRecordModel.FindCustom(l.ctx, builder, &recordSummary)
	if err != nil {
		l.Errorf("PrizeRecordSummary PrizeRecordModel.FindCustom err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	return &types.PrizeRecordSummaryResp{
		TotalWins:       recordSummary.TotalWins,
		RedemptionCount: recordSummary.RedemptionCount,
	}, nil
}
