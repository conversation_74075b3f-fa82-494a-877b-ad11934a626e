package prize

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfUpdareLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfUpdareLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfUpdareLogic {
	return &PrizeConfUpdareLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfUpdareLogic) PrizeConfUpdare(req *types.PrizeConfUpdareReq) error {
	// 1. 查找要更新的奖品配置
	existingPrize, err := l.svcCtx.PrizeConfModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if errors.Is(err, model.ErrNotFound) {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Errorf("PrizeConfUpdare.PrizeConfModel.FindOne err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	if req.PrizeCt < existingPrize.WinCt {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "中奖次数不能小于已中奖次数")
	}

	// 2. 验证同一批次同一大区下奖品名称是否已存在（排除当前记录）
	builder := l.svcCtx.PrizeConfModel.RowBuilder().
		Where(squirrel.Eq{"batch_id": existingPrize.BatchId}).
		Where(squirrel.Eq{"region_code": existingPrize.RegionCode}).
		Where(squirrel.Eq{"prize_name": req.PrizeName}).
		Where(squirrel.NotEq{"id": req.Id}). // 排除当前记录
		Limit(1)

	_, err = l.svcCtx.PrizeConfModel.FindOneByQuery(l.ctx, builder)
	if err != nil && err != model.ErrNotFound {
		l.Errorf("PrizeConfUpdare.PrizeConfModel.FindOneByQuery err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}
	if err == nil {
		// 找到了重复的奖品名称
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "该大区下已存在相同名称的奖品")
	}

	// 3. 更新奖品配置（保留原有的BatchId、RegionCode、RegionName、WinCt等字段）
	existingPrize.PrizeName = req.PrizeName
	existingPrize.Status = req.Status
	existingPrize.Points = req.Points
	existingPrize.RedeemQuantity = req.RedeemQuantity
	existingPrize.PrizeCt = req.PrizeCt
	existingPrize.ValidDays = req.ValidDays
	// 注意：WinCt 不更新，保持原有值

	// 4. 保存更新
	err = l.svcCtx.PrizeConfModel.Update(l.ctx, existingPrize)
	if err != nil {
		l.Errorf("PrizeConfUpdare.PrizeConfModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
