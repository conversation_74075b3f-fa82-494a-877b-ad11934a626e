package prize

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"strconv"
	"sync"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
)

type GenerateCardsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateCardsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateCardsLogic {
	return &GenerateCardsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// generateUniqueCardNo 生成唯一卡号：PC + 13位时间戳 + 5位随机数（大小写字母和数字）
func (l *GenerateCardsLogic) generateUniqueCardNo() string {
	// PC前缀
	prefix := "PC"

	// 13位时间戳（毫秒级）
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	if len(timestamp) > 13 {
		timestamp = timestamp[:13]
	}

	// 5位随机字符串（大小写字母和数字）
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	randomStr := ""
	for i := 0; i < 5; i++ {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		randomStr += string(charset[n.Int64()])
	}

	return prefix + timestamp + randomStr
}

// checkCardNoExists 检查卡号是否已存在
func (l *GenerateCardsLogic) checkCardNoExists(cardNo string) (bool, error) {
	_, err := l.svcCtx.PrizeCardModel.FindOneByNo(l.ctx, cardNo)
	if err != nil {
		if err == model.ErrNotFound {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// generateUniqueCardNos 生成指定数量的唯一卡号
func (l *GenerateCardsLogic) generateUniqueCardNos(count uint64) ([]string, error) {
	cardNos := make([]string, 0, count)
	maxRetries := count * 3 // 最多重试3倍数量，避免无限循环

	for len(cardNos) < int(count) && maxRetries > 0 {
		cardNo := l.generateUniqueCardNo()

		// 检查是否已存在
		exists, err := l.checkCardNoExists(cardNo)
		if err != nil {
			l.Errorf("checkCardNoExists error: %v", err)
			maxRetries--
			continue
		}

		if !exists {
			cardNos = append(cardNos, cardNo)
		}
		maxRetries--
	}

	if len(cardNos) < int(count) {
		return nil, fmt.Errorf("failed to generate enough unique card numbers, generated: %d, required: %d", len(cardNos), count)
	}

	return cardNos, nil
}

// generateQRCodeBatch 批量生成二维码（每批10个）
func (l *GenerateCardsLogic) generateQRCodeBatch(cardNos []string, batchId uint64) error {

	// 分批处理，每批10个
	batchSize := 10
	var wg sync.WaitGroup

	for i := 0; i < len(cardNos); i += batchSize {
		end := i + batchSize
		if end > len(cardNos) {
			end = len(cardNos)
		}

		batch := cardNos[i:end]
		wg.Add(1)

		go func(batch []string) {
			defer wg.Done()

			var eg errgroup.Group

			for _, cardNo := range batch {
				cardNo := cardNo // 避免闭包问题
				eg.Go(func() error {
					return l.processCard(cardNo, batchId)
				})
			}

			if err := eg.Wait(); err != nil {
				l.Errorf("generateQRCodeBatch error: %v", err)
			}
		}(batch)
	}

	wg.Wait()
	return nil
}

// processCard 处理单个卡片：生成二维码并保存到数据库
func (l *GenerateCardsLogic) processCard(cardNo string, batchId uint64) error {
	// 生成二维码
	qrResp, err := l.svcCtx.QRCodeService.GenerateEncryptedQRCode(cardNo)
	if err != nil {
		l.Errorf("GenerateEncryptedQRCode failed for cardNo %s: %v", cardNo, err)
		return err
	}

	if qrResp.ErrorCode != 0 {
		l.Errorf("QR code service returned error for cardNo %s: error_code=%d", cardNo, qrResp.ErrorCode)
		return fmt.Errorf("QR code service error: %d", qrResp.ErrorCode)
	}

	// 保存到数据库
	prizeCard := &model.VhPrizeCard{
		BatchId:   batchId,
		No:        cardNo,
		Status:    1, // 1未使用
		Uid:       0, // 未使用时为0
		RecordId:  0, // 未使用时为0
		QrcodeUrl: qrResp.Data.FinalUrl,
	}

	_, err = l.svcCtx.PrizeCardModel.Insert(l.ctx, prizeCard)
	if err != nil {
		l.Errorf("Insert prize card failed for cardNo %s: %v", cardNo, err)
		return err
	}

	l.Infof("Successfully generated and saved prize card: %s", cardNo)
	return nil
}

func (l *GenerateCardsLogic) GenerateCards(req *types.GenerateCardsReq) error {
	l.Infof("Starting to generate %d prize cards for batch %d", req.Num, req.BatchId)

	// 异步处理，立即返回成功
	go func() {
		// 生成唯一卡号
		cardNos, err := l.generateUniqueCardNos(req.Num)
		if err != nil {
			logx.Errorf("generateUniqueCardNos failed: %v", err)
			return
		}

		logx.Infof("Generated %d unique card numbers for batch %d", len(cardNos), req.BatchId)

		// 批量生成二维码并保存
		if err := l.generateQRCodeBatch(cardNos, req.BatchId); err != nil {
			logx.Errorf("generateQRCodeBatch failed: %v", err)
			return
		}

		logx.Infof("Successfully completed generating %d prize cards for batch %d", req.Num, req.BatchId)
	}()

	return nil
}
