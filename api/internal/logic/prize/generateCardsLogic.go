package prize

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"strconv"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateCardsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateCardsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateCardsLogic {
	return &GenerateCardsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// generateUniqueCardNo 生成唯一卡号：PC + 13位时间戳 + 5位随机数（大小写字母和数字）
func (l *GenerateCardsLogic) generateUniqueCardNo() string {
	// PC前缀
	prefix := "PC"

	// 13位时间戳（毫秒级）
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	if len(timestamp) > 13 {
		timestamp = timestamp[:13]
	}

	// 5位随机字符串（大小写字母和数字）
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	randomStr := ""
	for i := 0; i < 5; i++ {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		randomStr += string(charset[n.Int64()])
	}

	return prefix + timestamp + randomStr
}

// checkCardNoExists 检查卡号是否已存在
func (l *GenerateCardsLogic) checkCardNoExists(cardNo string) (bool, error) {
	_, err := l.svcCtx.PrizeCardModel.FindOneByNo(l.ctx, cardNo)
	if err != nil {
		if err == model.ErrNotFound {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// generateBatchUniqueCardNos 生成10个唯一卡号，如果存在重复则重新生成整批
func (l *GenerateCardsLogic) generateBatchUniqueCardNos() ([]string, error) {
	const batchSize = 10
	maxRetries := 100 // 最多重试100次，避免无限循环

	for retry := 0; retry < maxRetries; retry++ {
		// 生成10个卡号
		cardNos := make([]string, 0, batchSize)
		for i := 0; i < batchSize; i++ {
			cardNo := l.generateUniqueCardNo()
			cardNos = append(cardNos, cardNo)
		}

		// 批量检查这10个卡号是否存在
		allUnique := true
		for _, cardNo := range cardNos {
			exists, err := l.checkCardNoExists(cardNo)
			if err != nil {
				l.Errorf("checkCardNoExists error: %v", err)
				allUnique = false
				break
			}
			if exists {
				l.Infof("Card number %s already exists, regenerating batch", cardNo)
				allUnique = false
				break
			}
		}

		if allUnique {
			return cardNos, nil
		}
	}

	return nil, fmt.Errorf("failed to generate unique card numbers after %d retries", maxRetries)
}

// generateQRCodesAndSave 为卡号生成二维码并批量保存
func (l *GenerateCardsLogic) generateQRCodesAndSave(cardNos []string, batchId uint64) error {
	if len(cardNos) == 0 {
		return fmt.Errorf("no card numbers provided")
	}

	// 为每个卡号生成二维码
	prizeCards := make([]*model.VhPrizeCard, 0, len(cardNos))

	for _, cardNo := range cardNos {
		// 生成二维码
		qrResp, err := l.svcCtx.QRCodeService.GenerateEncryptedQRCode(cardNo)
		if err != nil {
			l.Errorf("GenerateEncryptedQRCode failed for cardNo %s: %v", cardNo, err)
			return err
		}

		if qrResp.ErrorCode != 0 {
			l.Errorf("QR code service returned error for cardNo %s: error_code=%d", cardNo, qrResp.ErrorCode)
			return fmt.Errorf("QR code service error: %d", qrResp.ErrorCode)
		}

		// 创建奖品卡对象
		prizeCard := &model.VhPrizeCard{
			BatchId:   batchId,
			No:        cardNo,
			Status:    1, // 1未使用
			Uid:       0, // 未使用时为0
			RecordId:  0, // 未使用时为0
			QrcodeUrl: qrResp.Data.FinalUrl,
		}
		prizeCards = append(prizeCards, prizeCard)
	}

	// 批量保存到数据库
	err := l.svcCtx.PrizeCardModel.BatchInsert(l.ctx, prizeCards)
	if err != nil {
		l.Errorf("BatchInsert prize cards failed: %v", err)
		return err
	}

	l.Infof("Successfully generated and saved %d prize cards for batch %d", len(cardNos), batchId)
	return nil
}

func (l *GenerateCardsLogic) GenerateCards(req *types.GenerateCardsReq) error {
	l.Infof("Starting to generate %d prize cards for batch %d", req.Num, req.BatchId)

	// 异步处理，立即返回成功
	go func() {
		// 计算需要生成多少批次（每批10个）
		totalBatches := (req.Num + 9) / 10 // 向上取整

		for i := uint64(0); i < totalBatches; i++ {
			// 生成10个唯一卡号
			cardNos, err := l.generateBatchUniqueCardNos()
			if err != nil {
				logx.Errorf("generateBatchUniqueCardNos failed: %v", err)
				return
			}

			// 如果是最后一批，可能不足10个
			remainingCards := req.Num - i*10
			if remainingCards < 10 {
				cardNos = cardNos[:remainingCards]
			}

			logx.Infof("Generated batch %d with %d unique card numbers for batch %d", i+1, len(cardNos), req.BatchId)

			// 为这批卡号生成二维码并批量保存
			if err := l.generateQRCodesAndSave(cardNos, req.BatchId); err != nil {
				logx.Errorf("generateQRCodesAndSave failed for batch %d: %v", i+1, err)
				return
			}

			logx.Infof("Successfully completed batch %d for batch %d", i+1, req.BatchId)
		}

		logx.Infof("Successfully completed generating %d prize cards for batch %d", req.Num, req.BatchId)
	}()

	return nil
}
