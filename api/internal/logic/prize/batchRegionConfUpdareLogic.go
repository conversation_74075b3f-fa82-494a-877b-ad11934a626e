package prize

import (
	"context"
	"engine/api/internal/logic"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchRegionConfUpdareLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchRegionConfUpdareLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchRegionConfUpdareLogic {
	return &BatchRegionConfUpdareLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchRegionConfUpdareLogic) BatchRegionConfUpdare(req *types.BatchRegionConfUpdareReq) error {
	// 查找批次
	batch, err := l.svcCtx.BatchRegionConfModel.FindOneByBatchIdRegionCode(l.ctx, req.BatchId, req.RegionCode)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("BatchRegionConfUpdare.BatchRegionConfModel.FindOneByBatchIdRegionCode err: %v", err)
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	if errors.Is(err, model.ErrNotFound) {
		//验证大区
		region := logic.GetRegionByCode(req.RegionCode)
		if region == nil {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "大区不存在")
		}
		//新增
		batch = &model.VhBatchRegionConf{
			BatchId:    req.BatchId,
			RegionCode: req.RegionCode,
			RegionName: region.Name,
			PrizeCt:    req.PrizeCt,
		}
		_, err = l.svcCtx.BatchRegionConfModel.Insert(l.ctx, batch)
		if err != nil {
			l.Errorf("BatchRegionConfUpdare.BatchRegionConfModel.Insert err: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
	} else {
		//更新
		batch.PrizeCt = req.PrizeCt
		err = l.svcCtx.BatchRegionConfModel.Update(l.ctx, batch)
		if err != nil {
			l.Errorf("BatchRegionConfUpdare.BatchRegionConfModel.Update err: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}
