package prize

import (
	"context"
	"engine/api/internal/logic"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfAddLogic {
	return &PrizeConfAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfAddLogic) PrizeConfAdd(req *types.PrizeConfAddReq) error {
	// 1. 验证大区代码是否有效
	region := logic.GetRegionByCode(req.RegionCode)
	if region == nil {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "无效的大区代码")
	}

	// 2. 验证同一批次同一大区下奖品名称是否已存在
	builder := l.svcCtx.PrizeConfModel.RowBuilder().
		Where(squirrel.Eq{"batch_id": req.BatchId}).
		Where(squirrel.Eq{"region_code": req.RegionCode}).
		Where(squirrel.Eq{"prize_name": req.PrizeName}).
		Limit(1)

	_, err := l.svcCtx.PrizeConfModel.FindOneByQuery(l.ctx, builder)
	if err != nil && err != model.ErrNotFound {
		l.Errorf("PrizeConfAdd.PrizeConfModel.FindOneByQuery err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}
	if err == nil {
		// 找到了重复的奖品名称
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "该大区下已存在相同名称的奖品")
	}

	// 3. 创建奖品配置
	prizeConf := &model.VhPrizeConf{
		BatchId:        req.BatchId,
		RegionCode:     req.RegionCode,
		RegionName:     region.Name,
		PrizeName:      req.PrizeName,
		Status:         req.Status,
		Points:         req.Points,
		RedeemQuantity: req.RedeemQuantity,
		PrizeCt:        req.PrizeCt,
		ValidDays:      req.ValidDays,
	}

	// 4. 保存到数据库
	_, err = l.svcCtx.PrizeConfModel.Insert(l.ctx, prizeConf)
	if err != nil {
		l.Errorf("PrizeConfAdd.PrizeConfModel.Insert err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
