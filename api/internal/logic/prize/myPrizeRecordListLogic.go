package prize

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MyPrizeRecordListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMyPrizeRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MyPrizeRecordListLogic {
	return &MyPrizeRecordListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MyPrizeRecordListLogic) MyPrizeRecordList(req *types.MyPrizeRecordListReq) (resp *types.MyPrizeRecordListResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	var (
		wait  errgroup.Group
		total int64
		list  []model.VhPrizeRecord
	)

	// 构建查询条件
	where := squirrel.And{squirrel.Eq{"uid": uid}}
	// 添加搜索条件
	if req.ShowUnredeemed {
		where = append(where, squirrel.Eq{"status": 1})
	}

	// 获取总数
	wait.Go(func() error {
		builder := model.CountBuilder("*", l.svcCtx.PrizeRecordModel.TableName()).Where(where)
		ct, er := l.svcCtx.PrizeRecordModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("MyPrizeRecordList.PrizeRecordModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := l.svcCtx.PrizeRecordModel.RowBuilder().
			Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit)).
			OrderBy("id desc")

		er := l.svcCtx.PrizeRecordModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("MyPrizeRecordList.PrizeRecordModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.MyPrizeRecordListResp)
	resp.Total = total
	resp.List = make([]types.MyPrizeRecordListInfo, 0, len(list))
	for _, ls := range list {
		info := types.MyPrizeRecordListInfo{
			Id:             ls.Id,
			GoosName:       ls.GoosName,
			PrizeName:      ls.PrizeName,
			PrizeTime:      common.TimeToString(ls.PrizeTime),
			Status:         ls.Status,
			QrcodeData:     ls.QrcodeData,
			RedemptionTime: common.TimeToString(ls.RedemptionTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
