package prize

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchUpdateLogic {
	return &BatchUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchUpdateLogic) BatchUpdate(req *types.BatchUpdateReq) error {
	batch, err := l.svcCtx.BatchModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			l.<PERSON>("BatchUpdate.BatchModel.FindOne err: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 解析开始时间和结束时间
	startTime, err := common.ParseTime(req.StartTime)
	if err != nil {
		l.Errorf("BatchAdd.ParseTime startTime err: %v", err)
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "开始时间格式错误")
	}

	endTime, err := common.ParseTime(req.EndTime)
	if err != nil {
		l.Errorf("BatchAdd.ParseTime endTime err: %v", err)
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "结束时间格式错误")
	}

	// 验证时间逻辑
	if !endTime.After(startTime) {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "结束时间必须大于开始时间")
	}

	batch.Title = req.Title
	batch.ShortCode = req.ShortCode
	batch.GoodsName = req.GoodsName
	batch.Desc = req.Desc
	batch.Status = req.Status
	batch.StartTime = startTime
	batch.EndTime = endTime
	err = l.svcCtx.BatchModel.Update(l.ctx, batch)
	if err != nil {
		l.Errorf("BatchUpdate.BatchModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
