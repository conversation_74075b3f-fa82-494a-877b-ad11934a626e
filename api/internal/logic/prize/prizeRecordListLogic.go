package prize

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeRecordListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeRecordListLogic {
	return &PrizeRecordListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeRecordListLogic) PrizeRecordList(req *types.PrizeRecordListReq) (resp *types.PrizeRecordListResp, err error) {
	var (
		wait            errgroup.Group
		total           int64
		redemptionCount int64
		list            []model.UserPrizeRecordListInfo
	)

	// 构建查询条件
	where := squirrel.And{}
	// 添加搜索条件
	if req.Status != 0 {
		where = append(where, squirrel.Eq{"p.status": req.Status})
	}
	if req.PrizeStartTime != "" && req.PrizeEndTime != "" {
		where = append(where, squirrel.GtOrEq{"p.prize_time": req.PrizeStartTime}, squirrel.LtOrEq{"p.prize_time": req.PrizeEndTime})
	}
	if req.RedemptionStartTime != "" && req.RedemptionEndTime != "" {
		where = append(where, squirrel.GtOrEq{"p.redemption_time": req.RedemptionStartTime}, squirrel.LtOrEq{"p.redemption_time": req.RedemptionEndTime})
	}
	if req.Keyword != "" {
		where = append(where, squirrel.Or{
			squirrel.Like{"u.phone": "%" + req.Keyword + "%"},
			squirrel.Like{"p.short_code": "%" + req.Keyword + "%"},
			squirrel.Like{"p.goos_name": "%" + req.Keyword + "%"},
			squirrel.Like{"p.prize_name": "%" + req.Keyword + "%"},
			squirrel.Like{"m.company_name": "%" + req.Keyword + "%"},
		})
	}

	// 获取总数
	wait.Go(func() error {
		builder := squirrel.Select("count(*) as ct").
			From(l.svcCtx.PrizeRecordModel.TableName() + " p").
			LeftJoin(l.svcCtx.UserModel.TableName() + " u on p.uid = u.id").
			LeftJoin(l.svcCtx.MerchantModel.TableName() + " m on p.mid = m.id").
			Where(where)
		ct, er := l.svcCtx.PrizeRecordModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("PrizeRecordList.PrizeRecordModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 获取已核销数量
	wait.Go(func() error {
		whereRedemption := append(where, squirrel.Eq{"p.status": 2}) // 状态2表示已核销
		builder := squirrel.Select("count(*) as ct").
			From(l.svcCtx.PrizeRecordModel.TableName() + " p").
			LeftJoin(l.svcCtx.UserModel.TableName() + " u on p.uid = u.id").
			LeftJoin(l.svcCtx.MerchantModel.TableName() + " m on p.mid = m.id").
			Where(whereRedemption)
		ct, er := l.svcCtx.PrizeRecordModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("PrizeRecordList.PrizeRecordModel.FindCount redemption err: %v", er)
			return er
		}
		redemptionCount = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := squirrel.Select("u.phone,m.company_name,p.*").
			From(l.svcCtx.PrizeRecordModel.TableName() + " p").
			LeftJoin(l.svcCtx.UserModel.TableName() + " u on p.uid = u.id").
			LeftJoin(l.svcCtx.MerchantModel.TableName() + " m on p.mid = m.id").
			Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit))

		if req.OrderBy != "" && req.OrderColumn != "" && common.InSlice(req.OrderColumn, []string{"prize_time", "redemption_time"}) && common.InSlice(req.OrderBy, []string{"asc", "desc"}) {
			builder = builder.OrderBy(fmt.Sprintf("p.%s %s", req.OrderColumn, req.OrderBy))
		} else {
			builder = builder.OrderBy("p.prize_time DESC")
		}

		er := l.svcCtx.PrizeRecordModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("PrizeRecordList.PrizeRecordModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.PrizeRecordListResp)
	resp.Total = total
	resp.RedemptionCount = redemptionCount
	resp.UnverifiedCount = total - redemptionCount
	resp.List = make([]types.PrizeRecordListInfo, 0, len(list))
	for _, ls := range list {
		info := types.PrizeRecordListInfo{
			Id:             ls.Id,
			Phone:          ls.Phone,
			ShortCode:      ls.ShortCode,
			GoosName:       ls.GoosName,
			PrizeName:      ls.PrizeName,
			PrizeTime:      common.TimeToString(ls.PrizeTime),
			Status:         ls.Status,
			CompanyName:    ls.CompanyName,
			RedemptionTime: common.TimeToString(ls.RedemptionTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
