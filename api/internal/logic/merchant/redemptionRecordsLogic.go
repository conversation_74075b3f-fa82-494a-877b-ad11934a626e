package merchant

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RedemptionRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRedemptionRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RedemptionRecordsLogic {
	return &RedemptionRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RedemptionRecordsLogic) RedemptionRecords(req *types.RedemptionRecordsReq) (resp *types.RedemptionRecordsResp, err error) {
	mid := l.svcCtx.Jwt.GetUid(l.ctx)
	var (
		wait  errgroup.Group
		total int64
		list  []model.VhMerchantRedemption
	)

	// 构建查询条件
	where := squirrel.And{squirrel.Eq{"mid": mid}}
	// 添加搜索条件
	if req.Status != 0 {
		where = append(where, squirrel.Eq{"status": req.Status})
	}

	if req.StartTime != "" && req.EndTime != "" {
		where = append(where, squirrel.GtOrEq{"create_time": req.StartTime})
		where = append(where, squirrel.LtOrEq{"create_time": req.EndTime})
	}

	// 获取总数
	wait.Go(func() error {
		builder := model.CountBuilder("*", l.svcCtx.MerchantRedemptionModel.TableName()).Where(where)
		ct, er := l.svcCtx.MerchantRedemptionModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("RedemptionRecords.MerchantRedemptionModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := l.svcCtx.MerchantRedemptionModel.RowBuilder().
			Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit)).
			OrderBy("create_time desc")

		er := l.svcCtx.MerchantRedemptionModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("RedemptionRecords.MerchantRedemptionModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.RedemptionRecordsResp)
	resp.Total = total
	resp.List = make([]types.RedemptionRecordsInfo, 0, len(list))
	for _, ls := range list {
		info := types.RedemptionRecordsInfo{
			Id:             ls.Id,
			GoodsName:      ls.GoodsName,
			RedeemQuantity: ls.RedeemQuantity,
			Address:        ls.Address,
			Consignee:      ls.Consignee,
			ContactPhone:   ls.ContactPhone,
			CreateTime:     common.TimeToString(ls.CreateTime),
			Status:         ls.Status,
			ShippingCode:   ls.ShippingCode,
			ShipTime:       common.TimeToString(ls.ShipTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
