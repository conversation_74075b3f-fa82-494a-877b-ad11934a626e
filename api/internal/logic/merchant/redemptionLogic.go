package merchant

import (
	"context"
	"database/sql"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RedemptionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRedemptionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RedemptionLogic {
	return &RedemptionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RedemptionLogic) Redemption(req *types.RedemptionReq) error {
	mid := l.svcCtx.MerchantJwt.GetUid(l.ctx)

	//获取用户剩余额度
	points, err := l.svcCtx.MerchantPointsModel.FindOne(l.ctx, req.Id)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.<PERSON>("Redemption MerchantPointsModel.FindOne err %v")
		return xerr.NewErrCode(xerr.DbError)
	}

	point := req.Count * 100

	if points == nil || points.Mid != mid || point > points.CurrentPoints {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "积分不足")
	}

	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		//减少积分
		merchantPointsUpdate := squirrel.Update(l.svcCtx.MerchantPointsModel.TableName()).
			Where(squirrel.Eq{"id": points.Id}).
			Where(squirrel.Expr("current_points >= ?", point)).
			Set("current_points", squirrel.Expr("current_points - ?", point))
		_, er := l.svcCtx.MerchantPointsModel.UpdateCustomTx(l.ctx, tx, merchantPointsUpdate)
		if er != nil {
			l.Errorf("Redemption MerchantPointsModel.UpdateCustomTx err %v", er)
			return er
		}

		//增加记录
		_, er = l.svcCtx.MerchantRedemptionModel.InsertTx(l.ctx, tx, &model.VhMerchantRedemption{
			Mid:            mid,
			ShortCode:      points.ShortCode,
			GoodsName:      points.GoodsName,
			RedeemQuantity: req.Count,
			ProvinceName:   req.ProvinceName,
			CityName:       req.CityName,
			DistrictName:   req.DistrictName,
			Address:        req.Address,
			Consignee:      req.Consignee,
			ContactPhone:   req.ContactPhone,
			Status:         1,
			ShipTime:       time.Date(1970, 1, 1, 0, 0, 0, 0, time.Local),
		})
		if er != nil {
			l.Errorf("Redemption MerchantRedemption err %v", er)
			return er
		}

		return nil
	})

	if err != nil {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "兑换失败，请稍后再试")
	}

	return nil
}
