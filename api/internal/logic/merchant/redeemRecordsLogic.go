package merchant

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RedeemRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRedeemRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RedeemRecordsLogic {
	return &RedeemRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RedeemRecordsLogic) RedeemRecords(req *types.RedeemRecordsReq) (resp *types.RedeemRecordsResp, err error) {
	mid := l.svcCtx.Jwt.GetUid(l.ctx)
	var (
		wait  errgroup.Group
		total int64
		list  []model.VhPrizeRecord
	)

	// 构建查询条件
	where := squirrel.And{squirrel.Eq{"mid": mid, "status": 2}}
	// 添加搜索条件
	if req.PrizeName != "" {
		where = append(where, squirrel.Eq{"prize_name": req.PrizeName})
	}

	if req.StartTime != "" && req.EndTime != "" {
		where = append(where, squirrel.GtOrEq{"redemption_time": req.StartTime})
		where = append(where, squirrel.LtOrEq{"redemption_time": req.EndTime})
	}

	// 获取总数
	wait.Go(func() error {
		builder := model.CountBuilder("*", l.svcCtx.PrizeRecordModel.TableName()).Where(where)
		ct, er := l.svcCtx.PrizeRecordModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("RedeemRecords.PrizeRecordModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := l.svcCtx.PrizeRecordModel.RowBuilder().
			Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit)).
			OrderBy("redemption_time desc")

		er := l.svcCtx.PrizeRecordModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("RedeemRecords.PrizeRecordModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.RedeemRecordsResp)
	resp.Total = total
	resp.List = make([]types.RedeemRecordsInfo, 0, len(list))
	for _, ls := range list {
		info := types.RedeemRecordsInfo{
			Id:             ls.Id,
			GoosName:       ls.GoosName,
			PrizeName:      ls.PrizeName,
			Points:         ls.Points,
			RedemptionTime: common.TimeToString(ls.RedemptionTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
