package merchant

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type BindingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBindingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindingLogic {
	return &BindingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BindingLogic) Binding(req *types.MerchantBindingReq) error {
	mid := l.svcCtx.MerchantJwt.GetUid(l.ctx)
	merchant, err := l.svcCtx.MerchantModel.FindOne(l.ctx, mid)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			l.<PERSON>("Binding.MerchantModel.FindOne err: %v", err)
		}
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	//只能是未提交或者已驳回
	if merchant.Status != 1 && merchant.Status != 4 {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "状态不正确")
	}

	//更新信息
	merchant.CompanyName = req.CompanyName
	merchant.ContactName = req.ContactName
	merchant.UnifiedSocialCode = req.UnifiedSocialCode
	merchant.BusinessLicenseImage = req.BusinessLicenseImage
	merchant.ProvinceId = req.ProvinceId
	merchant.CityId = req.CityId
	merchant.DistrictId = req.DistrictId
	merchant.ProvinceName = req.ProvinceName
	merchant.CityName = req.CityName
	merchant.DistrictName = req.DistrictName
	merchant.Address = req.Address
	merchant.Status = 2
	merchant.Reason = ""

	err = l.svcCtx.MerchantModel.Update(l.ctx, merchant)
	if err != nil {
		l.Errorf("Binding.MerchantModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
