package merchant

import (
	"context"
	"database/sql"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeLogic {
	return &ChangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChangeLogic) Change(req *types.MerchantChangeReq) error {
	if !req.IsPass && req.Reason == "" {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "请输入驳回原因")
	}

	merchant, err := l.svcCtx.MerchantModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			l.<PERSON>("Change.MerchantModel.FindOne err: %v", err)
		}
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	if merchant.Status != 2 {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "状态不正确")
	}

	if req.IsPass {
		merchant.Status = 3
		//检查是否存在，不存在时同步到供应商里面去
		_, err = l.svcCtx.SupplierModel.FindOneByQuery(l.ctx, l.svcCtx.SupplierModel.RowBuilder().Where(squirrel.Eq{"supplier_name": merchant.CompanyName}))
		if err != nil && errors.Is(err, model.ErrNotFound) {
			//新增
			supplier := &model.VhSupplier{
				SupplierName:     merchant.CompanyName,
				SupplierTel:      sql.NullString{String: merchant.Phone, Valid: true},
				SupplierDirector: sql.NullString{String: merchant.ContactName, Valid: true},
				RaxNo:            merchant.UnifiedSocialCode,
				Remark:           sql.NullString{String: "木兰朵抽奖商家后台", Valid: true},
			}
			_, _ = l.svcCtx.SupplierModel.Insert(l.ctx, supplier)
		}
	} else {
		merchant.Status = 4
		merchant.Reason = req.Reason
	}

	err = l.svcCtx.MerchantModel.Update(l.ctx, merchant)
	if err != nil {
		l.Errorf("Change.MerchantModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
