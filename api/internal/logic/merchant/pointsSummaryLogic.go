package merchant

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PointsSummaryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPointsSummaryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PointsSummaryLogic {
	return &PointsSummaryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PointsSummaryLogic) PointsSummary() (resp *types.PointsSummaryResp, err error) {
	mid := l.svcCtx.MerchantJwt.GetUid(l.ctx)

	var list []model.VhMerchantPoints

	builder := l.svcCtx.MerchantPointsModel.RowBuilder().Where(squirrel.Eq{"mid": mid})
	err = l.svcCtx.MerchantPointsModel.FindRows(l.ctx, builder, &list)
	if err != nil {
		l.Errorf("PointsSummary MerchantPointsModel.FindAllByMid err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.PointsSummaryResp)
	resp.List = make([]types.PointsInfo, 0, len(list))
	for _, points := range list {
		resp.List = append(resp.List, types.PointsInfo{
			Id:            points.Id,
			GoodsName:     points.GoodsName,
			TotalPoints:   points.TotalPoints,
			CurrentPoints: points.CurrentPoints,
		})
	}

	return
}
