package logic

// 省份信息
type Province struct {
	ID   int    // 省份ID
	Name string // 省份名称
}

// 大区配置
type RegionConfig struct {
	Code      string     // 大区代码
	Name      string     // 大区名称
	Provinces []Province // 包含的省份
}

// DefaultProvincesId 默认华东
const DefaultProvincesId = 10

// 全局地区配置
var (
	regions          []RegionConfig
	provinceToRegion map[int]string // 省份ID到大区code的映射
)

// 初始化地区配置
func init() {
	// 1. 定义所有大区及其包含的省份
	regions = []RegionConfig{
		{
			Code: "EC",
			Name: "华东",
			Provinces: []Province{
				{ID: 10, Name: "上海"},
				{ID: 16, Name: "山东"},
				{ID: 11, Name: "江苏"},
				{ID: 12, Name: "浙江"},
				{ID: 13, Name: "安徽"},
				{ID: 15, Name: "江西"},
				{ID: 14, Name: "福建"},
			},
		},
		{
			Code: "SC",
			Name: "华南",
			Provinces: []Province{
				{ID: 20, Name: "广东"},
				{ID: 21, Name: "广西壮族自治区"},
				{ID: 22, Name: "海南"},
			},
		},
		{
			Code: "CC",
			Name: "华中",
			Provinces: []Province{
				{ID: 17, Name: "河南"},
				{ID: 18, Name: "湖北"},
				{ID: 19, Name: "湖南"},
			},
		},
		{
			Code: "SW",
			Name: "西南",
			Provinces: []Province{
				{ID: 23, Name: "重庆"},
				{ID: 24, Name: "四川"},
				{ID: 25, Name: "贵州"},
				{ID: 26, Name: "云南"},
				{ID: 27, Name: "西藏自治区"},
			},
		},
	}

	// 2. 构建省份到大区的映射
	provinceToRegion = make(map[int]string)
	for _, region := range regions {
		for _, province := range region.Provinces {
			provinceToRegion[province.ID] = region.Code
		}
	}
}

// 获取所有大区配置
func GetAllRegions() []RegionConfig {
	return regions
}

// 通过大区code获取大区信息
func GetRegionByCode(regionCode string) *RegionConfig {
	for _, region := range regions {
		if region.Code == regionCode {
			return &region
		}
	}
	return nil
}

// 通过省份ID获取所属大区
func GetRegionByProvinceID(provinceID int) *RegionConfig {
	regionID, ok := provinceToRegion[provinceID]
	if !ok {
		return nil
	}
	return GetRegionByCode(regionID)
}

// 通过省份名称获取所属大区
func GetRegionByProvinceName(provinceName string) *RegionConfig {
	for _, region := range regions {
		for _, province := range region.Provinces {
			if province.Name == provinceName {
				return &region
			}
		}
	}
	return nil
}

func GetProvinceIdByProvinceName(provinceName string) int {
	for _, region := range regions {
		for _, province := range region.Provinces {
			if province.Name == provinceName {
				return province.ID
			}
		}
	}
	return 0
}
