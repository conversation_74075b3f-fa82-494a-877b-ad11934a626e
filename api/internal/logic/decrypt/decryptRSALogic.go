package decrypt

import (
	"context"
	"database/sql"
	"engine/api/internal/logic"
	"engine/api/internal/service"
	"engine/common/model"
	"engine/common/vine"
	"engine/common/xerr"
	"errors"
	"fmt"
	"github.com/Masterminds/squirrel"
	"math/rand"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptRSALogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptRSALogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptRSALogic {
	return &DecryptRSALogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptRSALogic) DecryptRSA(req *types.PrizeDecryptReq) (resp *types.PrizeDecryptResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	req.ProvinceId = 0

	//拿到用户地区
	ipInfo, err := vine.GetCityByIp(l.svcCtx.Config.ITEM.NaliIpUrl, req.XRealIP)
	if err != nil {
		return nil, xerr.NewErrMsg("获取地区失败，请重试")
	}
	ipArr := strings.Split(ipInfo, "|")
	fmt.Println(ipArr)
	if ipInfo == "" || len(ipArr) != 4 {
		//使用默认省
		req.ProvinceId = logic.DefaultProvincesId
	} else {
		req.ProvinceId = logic.GetProvinceIdByProvinceName(ipArr[1])
		if req.ProvinceId == 0 {
			//使用默认省
			req.ProvinceId = logic.DefaultProvincesId
		}
	}
	fmt.Println(req.ProvinceId)

	// 创建解密服务
	decryptService := service.NewDecryptService(l.svcCtx.Config)

	// 调用RSA解密服务
	decryptedData, err := decryptService.DecryptRSA(req.Q)
	if err != nil {
		l.Errorf("DecryptRSA error: %v", err)
		return nil, xerr.NewErrMsg("解密失败")
	}

	//唯一号验证
	card, err := l.svcCtx.PrizeCardModel.FindOneByNo(l.ctx, decryptedData)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("DecryptRSA PrizeCardModel.FindOneByNo err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if errors.Is(err, model.ErrNotFound) {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	if card.Status == 2 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "此二维码已被兑换")
	}

	if card.Status == 3 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "此活动已结束")
	}

	//验证批次
	batch, err := l.svcCtx.BatchModel.FindOne(l.ctx, card.BatchId)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("DecryptRSA BatchModel.FindOne err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	if batch == nil || batch.Status == 2 || batch.EndTime.Before(time.Now()) {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "活动已结束")
	}

	//拿到大区
	region := logic.GetRegionByProvinceID(req.ProvinceId)
	if region == nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "当前地区不支持此活动")
	}

	//拿到所有已启动的活动
	var prizeConfs []model.VhPrizeConf
	builder := l.svcCtx.PrizeConfModel.RowBuilder().
		Where(squirrel.Eq{"batch_id": card.BatchId}).
		Where(squirrel.Eq{"region_code": region.Code}).
		Where(squirrel.Eq{"status": 1}).
		Where(squirrel.Gt{"prize_ct": "win_ct"})
	err = l.svcCtx.PrizeConfModel.FindRows(l.ctx, builder, &prizeConfs)
	if err != nil {
		l.Errorf("DecryptRSA PrizeConfModel.FindRows err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if len(prizeConfs) == 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "当前地区活动已结束")
	}

	//抽奖池，按照剩余次数生成
	prizeIdPool := make([]uint64, 0)
	prizeMp := make(map[uint64]model.VhPrizeConf)
	for _, conf := range prizeConfs {
		prizeMp[conf.Id] = conf
		ct := conf.PrizeCt - conf.WinCt
		for i := 0; i < int(ct); i++ {
			prizeIdPool = append(prizeIdPool, conf.Id)
		}
	}

	//抽奖
	prizeId := prizeIdPool[rand.Intn(len(prizeIdPool))]
	prize := prizeMp[prizeId]

	//生成数据
	t := time.Now()
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		row, er := l.svcCtx.PrizeRecordModel.InsertTx(l.ctx, tx, &model.VhPrizeRecord{
			Uid:            uid,
			BatchId:        int64(card.BatchId),
			ShortCode:      batch.ShortCode,
			GoosName:       batch.GoodsName,
			PrizeId:        prizeId,
			PrizeName:      prize.PrizeName,
			PrizeTime:      t,
			Status:         1,
			ExpireTime:     t.Add(time.Hour * 24 * time.Duration(prize.ValidDays)),
			RedemptionTime: time.Date(1970, 1, 1, 0, 0, 0, 0, time.Local),
			QrcodeData:     req.Q,
			Points:         prize.Points,
		})
		if er != nil {
			l.Errorf("DecryptRSA PrizeRecordModel.InsertTx err %v", er)
			return er
		}
		insertId, er := row.LastInsertId()
		if er != nil {
			l.Errorf("DecryptRSA PrizeRecordModel.LastInsertId err %v", er)
			return er
		}

		prizeConfUpdateBuilder := squirrel.Update(l.svcCtx.PrizeConfModel.TableName()).
			Where(squirrel.Eq{"id": prize.Id}).
			Where(squirrel.Expr("win_ct + 1 <= prize_ct")).
			Set("win_ct", squirrel.Expr("win_ct + 1"))
		_, er = l.svcCtx.PrizeConfModel.UpdateCustomTx(l.ctx, tx, prizeConfUpdateBuilder)
		if er != nil {
			l.Errorf("DecryptRSA PrizeConfModel.Update err %v", er)
			return er
		}

		prizeCardUpdateBuilder := squirrel.Update(l.svcCtx.PrizeCardModel.TableName()).
			Where(squirrel.Eq{"id": card.Id}).
			Where(squirrel.Eq{"status": 1}).
			Set("status", 2).
			Set("uid", uid).
			Set("record_id", insertId)
		_, er = l.svcCtx.PrizeCardModel.UpdateCustomTx(l.ctx, tx, prizeCardUpdateBuilder)
		if er != nil {
			l.Errorf("DecryptRSA PrizeCardModel.Update err %v", er)
			return er
		}

		return nil
	})

	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "抽奖繁忙，请重试")
	}

	return &types.PrizeDecryptResp{
		PrizeName:  prize.PrizeName,
		GoodsName:  batch.GoodsName,
		ExpireTime: t.Add(time.Hour * 24 * time.Duration(prize.ValidDays)).Format("2006-01-02 15:04:05"),
	}, nil
}
