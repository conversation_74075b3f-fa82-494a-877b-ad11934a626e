package decrypt

import (
	"context"
	"database/sql"
	"engine/api/internal/service"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeRedemptionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeRedemptionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeRedemptionLogic {
	return &PrizeRedemptionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeRedemptionLogic) PrizeRedemption(req *types.PrizeRedemptionReq) (resp *types.PrizeRedemptionResp, err error) {
	mid := l.svcCtx.MerchantJwt.GetUid(l.ctx)

	// 创建解密服务
	decryptService := service.NewDecryptService(l.svcCtx.Config)

	// 调用RSA解密服务
	decryptedData, err := decryptService.DecryptRSA(req.Q)
	if err != nil {
		l.Errorf("PrizeRedemption error: %v", err)
		return nil, xerr.NewErrMsg("解密失败")
	}

	//唯一号验证
	card, err := l.svcCtx.PrizeCardModel.FindOneByNo(l.ctx, decryptedData)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("PrizeRedemption PrizeCardModel.FindOneByNo err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if errors.Is(err, model.ErrNotFound) {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	if card.Status != 2 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "二维码无效或已核销")
	}

	//拿到中奖记录
	record, err := l.svcCtx.PrizeRecordModel.FindOne(l.ctx, card.RecordId)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("PrizeRedemption PrizeRecordModel.FindOne err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	if record == nil || record.Status != 1 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "此二维码无效或已过期")
	}

	if record.ExpireTime.Before(time.Now()) {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "此二维码已过期")
	}

	//拿到核销公司
	merchant, err := l.svcCtx.MerchantModel.FindOne(l.ctx, mid)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("PrizeRedemption MerchantModel.FindOne err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	if merchant == nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "核销公司不存在")
	}

	//拿到商家积分
	merchantPoints, err := l.svcCtx.MerchantPointsModel.FindOneByMidShortCode(l.ctx, mid, record.ShortCode)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("PrizeRedemption MerchantPointsModel.FindOneByMidShortCode err %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	//核销
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		//记录核销
		prizeRecordUpdateBuilder := squirrel.Update(l.svcCtx.PrizeRecordModel.TableName()).
			Where(squirrel.Eq{"id": record.Id}).
			Set("status", 2).
			Set("mid", mid).
			Set("redemption_time", time.Now())
		_, er := l.svcCtx.PrizeRecordModel.UpdateCustomTx(l.ctx, tx, prizeRecordUpdateBuilder)
		if er != nil {
			l.Errorf("PrizeRedemptionPrizeRecordModel.UpdateCustomTx err %v", er)
			return er
		}

		//商家积分增加
		if merchantPoints == nil {
			_, er = l.svcCtx.MerchantPointsModel.InsertTx(l.ctx, tx, &model.VhMerchantPoints{
				Mid:           mid,
				ShortCode:     record.ShortCode,
				GoodsName:     record.GoosName,
				TotalPoints:   record.Points,
				CurrentPoints: record.Points,
			})
			if er != nil {
				l.Errorf("PrizeRedemptionMerchantPointsModel.InsertTx err %v", er)
				return er
			}
		} else {
			_, er = l.svcCtx.MerchantPointsModel.UpdateCustomTx(l.ctx, tx, squirrel.Update(l.svcCtx.MerchantPointsModel.TableName()).
				Where(squirrel.Eq{"id": merchantPoints.Id}).
				Set("total_points", merchantPoints.TotalPoints+record.Points).
				Set("current_points", merchantPoints.CurrentPoints+record.Points))
			if er != nil {
				l.Errorf("PrizeRedemptionMerchantPointsModel.UpdateCustomTx err %v", er)
				return er
			}
		}

		return nil
	})
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "核销失败，请稍后再试")
	}

	return &types.PrizeRedemptionResp{
		PrizeName:      record.PrizeName,
		RedemptionTime: common.TimeToString(time.Now()),
		CompanyName:    merchant.CompanyName,
	}, nil

}
