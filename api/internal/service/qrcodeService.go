package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"engine/common/config"

	"github.com/zeromicro/go-zero/core/logx"
)

// QRCodeService 二维码服务
type QRCodeService struct {
	config *config.ApiConfig
	logger logx.Logger
}

// NewQRCodeService 创建二维码服务实例
func NewQRCodeService(config *config.ApiConfig) *QRCodeService {
	return &QRCodeService{
		config: config,
		logger: logx.WithContext(nil),
	}
}

// GenerateEncryptedQRCodeRequest 生成加密二维码请求
type GenerateEncryptedQRCodeRequest struct {
	OriginalText   string `json:"original_text"`
	AesKey         string `json:"aes_key"`
	MiniappJumpUrl string `json:"miniapp_jump_url"`
	QrcodeUrl      string `json:"qrcode_url"`
	ShortenUrlType string `json:"shorten_url_type,omitempty"`
	OutputType     string `json:"output_type,omitempty"`
}

// GenerateEncryptedQRCodeResponse 生成加密二维码响应
type GenerateEncryptedQRCodeResponse struct {
	ErrorCode int `json:"error_code"`
	Data      struct {
		EncryptedContent string `json:"encrypted_content"`
		FinalUrl         string `json:"final_url"`
	} `json:"data"`
}

// ExportExcelRequest Excel导出请求
type ExportExcelRequest struct {
	Data []ExcelDataItem `json:"data"`
}

// ExcelDataItem Excel数据项
type ExcelDataItem struct {
	QRCode     string `json:"qrcode"`
	CardNumber string `json:"card_number"`
}

// GenerateEncryptedQRCode 生成加密二维码
func (s *QRCodeService) GenerateEncryptedQRCode(originalText string) (*GenerateEncryptedQRCodeResponse, error) {
	// 构建请求数据
	reqData := GenerateEncryptedQRCodeRequest{
		OriginalText:   originalText,
		AesKey:         s.config.QRCODE.AesKey,
		MiniappJumpUrl: s.config.QRCODE.MiniappJumpUrl,
		QrcodeUrl:      s.config.QRCODE.QrcodeUrl,
		ShortenUrlType: s.config.QRCODE.ShortenUrlType,
		OutputType:     s.config.QRCODE.OutputType,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		s.logger.Errorf("GenerateEncryptedQRCode marshal request error: %v", err)
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求URL
	url := s.config.ITEM.EncQrcodeUrl + "/generate-encrypted-qrcode"

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Errorf("GenerateEncryptedQRCode create request error: %v", err)
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Errorf("GenerateEncryptedQRCode request error: %v", err)
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体内容
	var buf bytes.Buffer
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		s.logger.Errorf("GenerateEncryptedQRCode read response body error: %v", err)
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	responseBody := buf.String()

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		s.logger.Errorf("GenerateEncryptedQRCode HTTP error: status_code=%d, body=%s", resp.StatusCode, responseBody)
		return nil, fmt.Errorf("二维码生成服务返回HTTP错误: %d", resp.StatusCode)
	}

	// 解析响应
	var response GenerateEncryptedQRCodeResponse
	if err := json.Unmarshal(buf.Bytes(), &response); err != nil {
		s.logger.Errorf("GenerateEncryptedQRCode decode response error: %v, response_body: %s", err, responseBody)
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if response.ErrorCode != 0 {
		s.logger.Errorf("GenerateEncryptedQRCode service error: error_code=%d", response.ErrorCode)
		return nil, fmt.Errorf("二维码生成服务返回错误: %d", response.ErrorCode)
	}

	return &response, nil
}

// ExportExcel 导出Excel
func (s *QRCodeService) ExportExcel(data []ExcelDataItem) ([]byte, error) {
	// 构建请求数据
	reqData := ExportExcelRequest{
		Data: data,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		s.logger.Errorf("ExportExcel marshal request error: %v", err)
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求URL
	url := s.config.ITEM.EncQrcodeUrl + "/export-excel"

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Errorf("ExportExcel create request error: %v", err)
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 60 * time.Second, // Excel导出可能需要更长时间
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Errorf("ExportExcel request error: %v", err)
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != 200 {
		s.logger.Errorf("ExportExcel HTTP error: status_code=%d", resp.StatusCode)
		return nil, fmt.Errorf("Excel导出服务返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应体（Excel文件二进制数据）
	var buf bytes.Buffer
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		s.logger.Errorf("ExportExcel read response error: %v", err)
		return nil, fmt.Errorf("读取响应数据失败: %v", err)
	}

	return buf.Bytes(), nil
}
