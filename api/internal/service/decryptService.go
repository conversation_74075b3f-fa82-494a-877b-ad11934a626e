package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"engine/common/config"
	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptService struct {
	config config.ApiConfig
	logger logx.Logger
}

// AES解密请求结构
type AESDecryptRequest struct {
	EncryptedData string `json:"encrypted_data"`
	AESKey        string `json:"aes_key"`
}

// RSA解密请求结构
type RSADecryptRequest struct {
	EncryptedData string `json:"encrypted_data"`
}

// 解密响应结构
type DecryptResponse struct {
	ErrorCode int `json:"error_code"`
	Data      struct {
		DecryptedData string `json:"decrypted_data"`
	} `json:"data"`
}

func NewDecryptService(config config.ApiConfig) *DecryptService {
	return &DecryptService{
		config: config,
		logger: logx.WithContext(nil),
	}
}

// DecryptAES 调用第三方AES解密服务
func (s *DecryptService) DecryptAES(encryptedData string) (string, error) {
	// 构建请求数据
	reqData := AESDecryptRequest{
		EncryptedData: encryptedData,
		AESKey:        s.config.QRCODE.AesKey,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		s.logger.Errorf("DecryptAES marshal request error: %v", err)
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求URL
	url := s.config.ITEM.EncQrcodeUrl + "/decrypt-aes"

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Errorf("DecryptAES create request error: %v", err)
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Errorf("DecryptAES send request error: %v", err)
		return "", fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Errorf("DecryptAES read response error: %v", err)
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		s.logger.Errorf("DecryptAES HTTP error: status=%d, body=%s", resp.StatusCode, string(body))
		return "", fmt.Errorf("HTTP请求失败: status=%d", resp.StatusCode)
	}

	// 解析响应
	var decryptResp DecryptResponse
	if err := json.Unmarshal(body, &decryptResp); err != nil {
		s.logger.Errorf("DecryptAES unmarshal response error: %v, body=%s", err, string(body))
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查业务错误码
	if decryptResp.ErrorCode != 0 {
		s.logger.Errorf("DecryptAES business error: error_code=%d", decryptResp.ErrorCode)
		return "", fmt.Errorf("解密失败: error_code=%d", decryptResp.ErrorCode)
	}

	return decryptResp.Data.DecryptedData, nil
}

// DecryptRSA 调用第三方RSA解密服务
func (s *DecryptService) DecryptRSA(encryptedData string) (string, error) {
	// 构建请求数据
	reqData := RSADecryptRequest{
		EncryptedData: encryptedData,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		s.logger.Errorf("DecryptRSA marshal request error: %v", err)
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求URL
	url := s.config.ITEM.EncQrcodeUrl + "/decrypt-rsa"

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Errorf("DecryptRSA create request error: %v", err)
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Errorf("DecryptRSA send request error: %v", err)
		return "", fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Errorf("DecryptRSA read response error: %v", err)
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		s.logger.Errorf("DecryptRSA HTTP error: status=%d, body=%s", resp.StatusCode, string(body))
		return "", fmt.Errorf("HTTP请求失败: status=%d", resp.StatusCode)
	}

	// 解析响应
	var decryptResp DecryptResponse
	if err := json.Unmarshal(body, &decryptResp); err != nil {
		s.logger.Errorf("DecryptRSA unmarshal response error: %v, body=%s", err, string(body))
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查业务错误码
	if decryptResp.ErrorCode != 0 {
		s.logger.Errorf("DecryptRSA business error: error_code=%d", decryptResp.ErrorCode)
		return "", fmt.Errorf("解密失败: error_code=%d", decryptResp.ErrorCode)
	}

	return decryptResp.Data.DecryptedData, nil
}
