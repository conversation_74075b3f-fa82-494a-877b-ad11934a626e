package task

import (
	"context"
	"engine/api/internal/svc"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type CheckExpiry struct {
	BaseTask
	SvcCtx *svc.ServiceContext
}

func (t *CheckExpiry) Execute() {
	startTime := time.Now()

	builder := squirrel.Update(t.SvcCtx.PrizeRecordModel.TableName()).
		Where(squirrel.Eq{"status": 1}).
		Where(squirrel.LtOrEq{"expire_time": time.Now()}).
		Set("status", 3)
	_, err := t.SvcCtx.PrizeRecordModel.UpdateCustom(context.Background(), builder)
	if err != nil {
		logx.Errorf("CheckExpiry PrizeRecordModel.UpdateCustom err: %v", err)
	}

	logx.Info(fmt.Sprintf("CheckExpiry执行完成，用时:%.2f ", time.Since(startTime).Seconds()))
}

func (t *CheckExpiry) Stop() {

}
