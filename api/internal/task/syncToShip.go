package task

import (
	"context"
	"engine/api/internal/svc"
	"engine/common/model"
	"engine/common/vine"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type SyncToShip struct {
	BaseTask
	SvcCtx *svc.ServiceContext
}

func (t *SyncToShip) Execute() {
	startTime := time.Now()

	//拿到所有伤害未同步的商品兑换记录
	var list []model.VhMerchantRedemption
	builder := t.SvcCtx.MerchantRedemptionModel.RowBuilder().Where(squirrel.Eq{"is_sync": 1})
	err := t.SvcCtx.MerchantRedemptionModel.FindRows(context.Background(), builder, &list)
	if err != nil {
		logx.Errorf("SyncToShip.MerchantRedemptionModel.FindRows err: %v", err)
	} else {
		for _, redemption := range list {
			mp := map[string]interface{}{
				"platform":  32,
				"storename": "木兰朵抽奖",
				"owner_id":  "mulando-redeem",
				"address": map[string]interface{}{
					"consignee":    redemption.Consignee,
					"cellphone":    redemption.ContactPhone,
					"address":      redemption.Address,
					"province":     redemption.ProvinceName,
					"city":         redemption.CityName,
					"district":     redemption.DistrictName,
					"express_fee":  0,
					"express_type": 4,
				},
				"goodsOrder": map[string]interface{}{
					"main": map[string]interface{}{
						"pay_money":    0,
						"payment_time": time.Now().Unix(),
						"create_time":  time.Now().Unix(),
						"goodsname":    redemption.GoodsName,
						"orderno":      fmt.Sprintf("mulandoredeem%d", redemption.Id),
					},
					"son": []map[string]interface{}{{
						"goodsname":     redemption.GoodsName,
						"orderno":       fmt.Sprintf("mulandoredeem%d", redemption.Id),
						"pay_number":    1,
						"pay_money":     0,
						"status":        1,
						"refund_status": 0,
						"store_code":    "262",
						"product": []map[string]interface{}{{
							"short_code": redemption.ShortCode,
							"number":     redemption.RedeemQuantity,
						}},
					}},
				},
			}

			err = vine.SyncOrderToVine(t.SvcCtx.Config.ITEM.ORDERS_URL, mp)
			if err == nil {
				//修改为同步成功
				redemption.IsSync = 2
				_ = t.SvcCtx.MerchantRedemptionModel.Update(context.Background(), &redemption)
			}
		}
	}

	logx.Info(fmt.Sprintf("SyncToShip执行完成，用时:%.2f ", time.Since(startTime).Seconds()))
}

func (t *SyncToShip) Stop() {

}
