package main

import (
	"engine/api/internal/task"
	cf "engine/common/config"
	_ "engine/common/logger"
	"fmt"
	"net/http"

	"engine/api/internal/config"
	"engine/api/internal/handler"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func main() {

	c := config.NewConfig()
	cf.InitApiConfig(c, "go-mulandoredeem", "vinehoo.conf", 0)

	server := rest.MustNewServer(c.RestConf, rest.WithCustomCors(func(header http.Header) {
		header.Set("Access-Control-Allow-Headers", "Content-Type, Origin")
		header.Set("Access-Control-Allow-Origin", "*")
		header.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	}, func(w http.ResponseWriter) {
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Stop()

	ctx := svc.NewServiceContext(*c)
	handler.RegisterHandlers(server, ctx)

	taskManager := task.NewTaskManager()

	//过期检查
	if _, err := taskManager.AddTask("0 * * * * *", &task.CheckExpiry{
		SvcCtx: ctx,
	}); err != nil {
		panic(err)
	}

	//同步到发货系统
	if _, err := taskManager.AddTask("0 */10 * * * *", &task.SyncToShip{
		SvcCtx: ctx,
	}); err != nil {
		panic(err)
	}

	taskManager.Start()

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
