syntax = "v1"

info(
    title: "商家"
    author: "gangh"
    email: "<EMAIL>"
    version: "v1"
)

type (
    MerchantBindingReq {
        CompanyName string `json:"company_name" validate:"max=50" v:"公司名称"`
        ContactName string `json:"contact_name" validate:"max=10" v:"联系人姓名"`
        UnifiedSocialCode string `json:"unified_social_code" validate:"required,alphanum" v:"社会统一代码"`
        BusinessLicenseImage string `json:"business_license_image" validate:"required" v:"营业执照图片"`
        ProvinceId uint64 `json:"province_id" validate:"required" v:"省id"`
        CityId uint64 `json:"city_id" validate:"required" v:"市id"`
        DistrictId uint64 `json:"district_id" validate:"required" v:"区id"`
        ProvinceName string `json:"province_name" validate:"required" v:"省名称"`
        CityName string `json:"city_name" validate:"required" v:"市名称"`
        DistrictName string `json:"district_name" validate:"required" v:"区名称"`
        Address string `json:"address" validate:"max=250" v:"详细地址"`
    }

    MerchantListReq {
        Paging
        Keyword string `form:"keyword,optional"`
        Status int64 `form:"status,optional"`
        OrderColumn string `form:"order_column,optional"`
        OrderBy string `form:"order_by,optional"`
    }
    MerchantListResp {
        List []MerchantInfo `json:"list"`
        Total int64 `json:"total"`
    }

    MerchantInfo {
        Id uint64 `json:"id"`
        Phone string `json:"phone"`                                 // 手机号
        CompanyName string `json:"company_name"`                    // 公司名称
        ContactName string `json:"contact_name"`                    // 联系人姓名
        UnifiedSocialCode string `json:"unified_social_code"`       // 社会统一代码
        BusinessLicenseImage string `json:"business_license_image"` // 营业执照图片
        ProvinceId uint64 `json:"province_id"`                      // 省id
        CityId uint64 `json:"city_id"`                              // 市id
        DistrictId uint64 `json:"district_id"`                      // 区id
        ProvinceName string `json:"province_name"`                  // 省名称
        CityName string `json:"city_name"`                          // 市名称
        DistrictName string `json:"district_name"`                  // 区名称
        Address string `json:"address"`                             // 完整地址(包含省市区)
        CreateTime string `json:"create_time"`                      // 创建时间
        Status uint64 `json:"status"`                               // 1未提交，2审核中，3通过，4驳回（可以再提交）
        Reason string `json:"reason"`                               // 驳回原因
        LastLoginTime string `json:"last_login_time"`               // 最后登录时间
    }

    MerchantChangeReq {
        IdJU
        IsPass bool `json:"is_pass" v:"是否通过"`
        Reason string `json:"reason,optional" validate:"max=250" v:"驳回原因"`
    }

    PointsSummaryResp {
        List []PointsInfo `json:"list"`
    }
    PointsInfo {
        Id uint64 `db:"id"`
        GoodsName string `db:"goods_name"`         // 商品名
        TotalPoints uint64 `db:"total_points"`     // 累计积分（单位分）
        CurrentPoints uint64 `db:"current_points"` // 当前积分（单位分）
    }

    RedeemRecordsReq {
        Paging
        StartTime string `form:"start_time,optional"`
        EndTime string `form:"end_time,optional"`
        PrizeName string `form:"prize_name,optional"`
    }
    RedeemRecordsResp {
        List []RedeemRecordsInfo `json:"list"`
        Total int64 `json:"total"`
    }
    RedeemRecordsInfo {
        Id uint64 `json:"id"`
        GoosName string `json:"goos_name"`                            // 商品名称
        PrizeName string `json:"prize_name"`                          // 奖品名
        Points uint64 `json:"points"`                                 // 积分
        RedemptionTime string `json:"redemption_time"`                // 核销时间
    }

    RedemptionReq {
        Id uint64 `json:"id" validate:"required" v:"积分id"`
        Count uint64 `json:"count" validate:"required" v:"兑换数量"`
        Consignee string `json:"consignee" v:"收货人"`
        ContactPhone string `json:"contact_phone" validate:"required" v:"联系电话"`
        Address string `json:"address" validate:"required" v:"详细地址"`
        ProvinceName string `json:"province_name" validate:"required" v:"省名称"`
        CityName string `json:"city_name" validate:"required" v:"市名称"`
        DistrictName string `json:"district_name" validate:"required" v:"区名称"`
    }

    RedemptionRecordsReq {
        Paging
        StartTime string `form:"start_time,optional"`
        EndTime string `form:"end_time,optional"`
        Status int64 `json:"status,optional"`
    }
    RedemptionRecordsResp {
        List []RedemptionRecordsInfo `json:"list"`
        Total int64 `json:"total"`
    }
    RedemptionRecordsInfo {
        Id uint64 `json:"id"`
        GoodsName string `json:"goods_name"`           // 商品名
        RedeemQuantity uint64 `json:"redeem_quantity"` // 兑换瓶数
        Address string `json:"address"`                // 完整收货地址(包含省市区)
        Consignee string `json:"consignee"`            // 收货人
        ContactPhone string `json:"contact_phone"`     // 联系人电话
        CreateTime string `json:"create_time"`         // 申请时间
        Status uint64 `json:"status"`                  // 1待发货，2已发货，3已签收
        ShippingCode string `json:"shipping_code"`     // 物流单号
        ShipTime string `json:"ship_time"`             // 发货时间
    }
)

//商家端接口
@server(
    middleware: Global
    group: merchant
    prefix: /mulandoRedeem/v1/merchant/miniprogram
    timeout: 3s
)

service mulandoRedeem {
    @handler WeChatAppletPhoneLogin //微信小程序一键登录
    post /oneclicklogin (WeChatAppletPhoneLoginReq) returns (WeChatAppletPhoneLoginResp)
}

//商家端接口
@server(
    middleware: Global,MerchantAuth
    group: merchant
    prefix: /mulandoRedeem/v1/merchant/miniprogram
    timeout: 3s
)

service mulandoRedeem {
    @handler Binding //绑定信息
    post /binding (MerchantBindingReq)

    @handler PointsSummary //积分统计
    get /pointsSummary returns (PointsSummaryResp)

    @handler RedeemRecords //核销记录
    get /redeemRecords (RedeemRecordsReq) returns (RedeemRecordsResp)

    @handler Redemption //兑换
    post /redemption (RedemptionReq)

    @handler RedemptionRecords //兑换记录
    get /redemptionRecords (RedemptionRecordsReq) returns (RedemptionRecordsResp)
}

//后台接口
@server(
    middleware: Global,Admin
    group: merchant
    prefix: /mulandoRedeem/v1/merchant/admin
    timeout: 3s
)

service mulandoRedeem {
    @handler MerchantList //列表
    get /merchantList (MerchantListReq) returns (MerchantListResp)

    @handler Change //审核
    post /change (MerchantChangeReq)
}