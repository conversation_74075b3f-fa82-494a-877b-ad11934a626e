syntax = "v1"

info(
    title: "兑换"
    author: "gangh"
    email: "<EMAIL>"
    version: "v1"
)

type (
    RedemptionListReq {
        Paging
        Keyword string `form:"keyword,optional"`
        Status int64 `form:"status,optional"`
        OrderColumn string `form:"order_column,optional"`
        OrderBy string `form:"order_by,optional"`
        StartTime string `json:"start_time,optional"`
        EndTime string `json:"end_time,optional"`
    }
    RedemptionListResp {
        List []RedemptionListInfo `json:"list"`
        Total int64 `json:"total"`
    }

    RedemptionListInfo {
        Id uint64 `json:"id"`
        CompanyName string `json:"company_name"`                      // 公司名称
        ShortCode string `json:"short_code"`                          // 商品简码
        GoodsName string `json:"goods_name"`                          // 商品名
        RedeemQuantity uint64 `json:"redeem_quantity"`                // 兑换瓶数
        ProvinceName string `json:"province_name"`                    // 省名称
        CityName string `json:"city_name"`                            // 市名称
        DistrictName string `json:"district_name"`                    // 区名称
        Address string `json:"address"`                               // 完整收货地址(包含省市区)
        Consignee string `json:"consignee"`                           // 收货人
        ContactPhone string `json:"contact_phone"`                    // 联系人电话
        CreateTime string `json:"create_time"`                        // 申请时间
        Status uint64 `json:"status"`                                 // 1待发货，2已发货，3已签收
        ShippingCode string `json:"shipping_code"`                    // 物流单号
        ShipTime string `json:"ship_time"`                            // 发货时间
    }

    ShipmentNotifyReq {
        MainOrderNo string `json:"main_order_no,optional" v:"主订单号"`
        OrderNo string `json:"orderNo" validate:"required" v:"子订单号"`
        WayBill string `json:"wayBill" validate:"required" v:"运单号"`
        ExpressType int64 `json:"expressType" validate:"required" v:"快递方式"`
        Platform int64 `json:"platform,optional" v:"平台"`
        IsUp int64 `json:"is_up,optional" v:"是否上架"`
    }
)

//发货通知接口
@server(
    middleware: Global
    group: notify
    prefix: /mulandoRedeem/v1/redemptions/notify
    timeout: 3s
)

service mulandoRedeem {
    // 发货通知
    @handler ShipmentNotify
    post /shipment (ShipmentNotifyReq)
}

//后台接口
@server(
    middleware: Global,Admin
    group: redemptions
    prefix: /mulandoRedeem/v1/redemptions/admin
    timeout: 3s
)

service mulandoRedeem {
    @handler RedemptionList //列表
    get /redemptionList (RedemptionListReq) returns (RedemptionListResp)
}