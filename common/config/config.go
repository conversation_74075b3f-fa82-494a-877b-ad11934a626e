package config

import (
	"engine/common/logger"
	"fmt"
	"github.com/vber/nacos/v2"
	"time"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/rest"
)

type ApiConfig struct {
	rest.RestConf
	Auth struct { // JWT 认证需要的密钥和过期时间配置
		AccessSecret         string
		AccessExpire         time.Duration
		MerchantAccessSecret string
		MerchantAccessExpire time.Duration
	}
	Redis cache.CacheConf
	Mysql struct {
		DataSource     string
		WikiDataSource string
	}
	ITEM struct {
		WECHART_URL  string
		ALIURL       string
		SmsUrl       string
		EncQrcodeUrl string
		QueueUrl     string
		ORDERS_URL   string
		NaliIpUrl    string
	}
	QRCODE struct {
		AesKey         string
		MiniappJumpUrl string
		QrcodeUrl      string
		ShortenUrlType string
		OutputType     string
	}
}

func InitApiConfig(ayn *ApiConfig, dataId, group string, operType int) {
	var data string
	defer func() {
		err := recover()
		if err != nil {
			logger.E(fmt.Sprintf("%s config init Error", dataId))
			panic(err)
		}
	}()

	data, _ = nacos.GetString(dataId, group, func(data *string, err error) {
		if err == nil {
			loadApiConfig(operType, *data, dataId, ayn)
		}
	})
	if data == "" {
		panic(fmt.Errorf("%s config is empty", dataId))
	}
	loadApiConfig(operType, data, dataId, ayn)
}

func loadApiConfig(operType int, data, dataId string, ayn *ApiConfig) {
	if operType == 0 {
		err := conf.LoadFromYamlBytes([]byte(data), ayn)
		if err != nil {
			panic(fmt.Errorf("%s config Yaml Error %s", dataId, err))
		}
	}
}
