package vine

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	Ok int64 = 0
)

type BaseResponse struct {
	Code int64       `json:"error_code"`
	Msg  string      `json:"error_msg"`
	Data interface{} `json:"data,optional"`
}

type RecommendationPushInfo struct {
	RecommendationPush string `json:"recommendation_push"`
}
type RecommendationPushResponse struct {
	Code int64  `json:"error_code"`
	Msg  string `json:"error_msg"`
	Data struct {
		List []RecommendationPushInfo `json:"list"`
	} `json:"data,optional"`
}

type CityResponse struct {
	Code int64               `json:"error_code"`
	Msg  string              `json:"error_msg"`
	Data []map[string]string `json:"data,optional"`
}

type Decode struct {
	result interface{}
}

func NewDecode(result interface{}) *Decode {
	return &Decode{result: result}
}

func (d *Decode) D(response *resty.Response, err error) (*resty.Response, error) {
	if err == nil {
		var base BaseResponse
		err = json.Unmarshal(response.Body(), &base)
		if err == nil {
			if base.Code == Ok {
				err = json.Unmarshal(response.Body(), d.result)
			} else {
				err = errors.New(base.Msg)
			}
		}
	}

	return response, err
}

func (d *Decode) De(response *resty.Response, err error) (*resty.Response, error) {
	if err == nil {
		err = json.Unmarshal(response.Body(), d.result)
	}

	return response, err
}

type Apis struct {
	ServiceHosts map[string]string
}

func NewApiService(serviceHosts map[string]string) *Apis {
	return &Apis{ServiceHosts: serviceHosts}
}

func (a *Apis) getHost(service string) (string, error) {
	if host, ok := a.ServiceHosts[service]; ok {
		return host, nil
	} else {
		return "", errors.New(fmt.Sprintf("服务%s未配置", service))
	}
}

func getResty() *resty.Client {
	return resty.New().OnBeforeRequest(func(client *resty.Client, request *resty.Request) error {
		logx.Info(fmt.Sprintf("vineApi url:%s", request.URL))
		return nil
	}).OnRequestLog(func(log *resty.RequestLog) error {
		logx.Info(fmt.Sprintf("request body:%s", log.Body))
		return nil
	}).OnResponseLog(func(log *resty.ResponseLog) error {
		logx.Info(fmt.Sprintf("response body:%s", log.Body))
		return nil
	})
}

func SetHost() *resty.Client {
	return resty.New().OnBeforeRequest(func(client *resty.Client, request *resty.Request) error {
		logx.Info(fmt.Sprintf("vineApi url:%s", request.URL))
		return nil
	}).OnRequestLog(func(log *resty.RequestLog) error {
		logx.Info(fmt.Sprintf("request body:%s", log.Body))
		return nil
	}).OnResponseLog(func(log *resty.ResponseLog) error {
		logx.Info(fmt.Sprintf("response body:%s", log.Body))
		return nil
	})
}

// SendWeMessageByText 向企业的应用发送消息
func (a *Apis) SendWeMessageByText(uid, message string) (*resty.Response, error) {
	host, err := a.getHost("wechat")
	if err != nil {
		return nil, err
	}
	var rsp BaseResponse
	return NewDecode(&rsp).D(getResty().SetTimeout(time.Second).R().
		SetBody(map[string]interface{}{"agentid": 1000015, "msgtype": "text", "content": message, "userid": uid}).
		Post(host + "/wechat/v3/wecom/app/send"))
}

// SendSms 发送短信
func SendSms(host, phone, content string) (resp *BaseResponse, err error) {
	var rsp BaseResponse
	resp = &rsp
	_, err = NewDecode(&rsp).D(SetHost().SetTimeout(30 * time.Second).R().
		SetBody(map[string]string{
			"telephone": phone,
			"content":   content,
		}).Post(host + "/sms/v3/group/sendSmsNote"))

	return
}

// ShortEn 生成短链接
func ShortEn(host, url string) (shortUrl string, err error) {
	var rsp BaseResponse
	_, err = NewDecode(&rsp).D(SetHost().SetTimeout(30 * time.Second).R().
		SetFormData(map[string]string{
			"url": url,
		}).Post(host + "/shorten"))
	if err != nil {
		return "", err
	}

	return cast.ToString(rsp.Data), nil
}

func SendRobotText(host, accessToken, at, content string) (err error) {
	var rsp BaseResponse
	_, err = NewDecode(&rsp).D(SetHost().SetTimeout(30*time.Second).R().
		SetHeader("vinehoo-client", "go-mulando-greate-destiny").
		SetBody(map[string]string{
			"exchange_name": "dingtalk",
			"routing_key":   "dingtalk_sender",
			"message_id":    "",
			"data":          base64.StdEncoding.EncodeToString([]byte(`{"access_token":"` + accessToken + `","at":"` + at + `","content":"` + base64.StdEncoding.EncodeToString([]byte(content)) + `","type":"text"}`)),
		}).Post(host + "/services/v3/queue/push"))
	return err
}

// SyncOrderToVine 同步订单到酒云网中台
func SyncOrderToVine(host string, mp map[string]interface{}) (err error) {
	var rsp BaseResponse
	_, err = NewDecode(&rsp).D(SetHost().SetTimeout(30*time.Second).R().
		SetHeader("vinehoo-client", "go-mulando-redeem").
		SetBody(mp).Post(host + "/orders/v3/tripartite/create"))
	return err
}

// GetCityByIp 根据ip获取省市信息
func GetCityByIp(host string, ip string) (string, error) {
	var rsp CityResponse
	_, err := NewDecode(&rsp).D(SetHost().SetTimeout(30*time.Second).R().
		SetHeader("vinehoo-client", "go-mulando-redeem").
		SetBody(map[string]interface{}{"ips": []string{ip}}).Post(host + "/services/v3/ip/check"))

	if err != nil {
		return "", err
	}

	if _, ok := rsp.Data[0][ip]; ok {
		return rsp.Data[0][ip], nil
	} else {
		return "", nil
	}
}
