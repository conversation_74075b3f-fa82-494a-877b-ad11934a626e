package function

import (
	"strings"
	"time"
)

// BuildImageURL 处理单图URL拼接
func BuildImageURL(baseURL, imagePath string) string {
	if imagePath == "" {
		return ""
	}
	return baseURL + imagePath
}

// NormalizeImagePath 标准化图片路径，确保返回半路径（去除域名前缀）
func NormalizeImagePath(imagePath string) string {
	if imagePath == "" {
		return ""
	}

	// 如果是完整URL，提取路径部分
	if strings.HasPrefix(imagePath, "http://") || strings.HasPrefix(imagePath, "https://") {
		// 查找第三个斜杠的位置（协议://域名/路径）
		slashCount := 0
		for i, char := range imagePath {
			if char == '/' {
				slashCount++
				if slashCount == 3 {
					return imagePath[i:] // 返回从第三个斜杠开始的路径
				}
			}
		}
	}

	// 如果不是完整URL，直接返回（假设已经是半路径）
	return imagePath
}

// ProcessSnapshotImage 处理快照中的图片字段，统一拼接域名
func ProcessSnapshotImage(baseURL, imagePath string) string {
	if imagePath == "" {
		return ""
	}

	// 如果已经是完整URL，直接返回
	if strings.HasPrefix(imagePath, "http://") || strings.HasPrefix(imagePath, "https://") {
		return imagePath
	}

	// 确保路径以斜杠开头
	if !strings.HasPrefix(imagePath, "/") {
		imagePath = "/" + imagePath
	}

	// 拼接域名
	return baseURL + imagePath
}

// BuildImageURLs 处理多图URL拼接，返回数组
func BuildImageURLs(baseURL, imagePaths string) []string {
	if imagePaths == "" {
		return []string{}
	}

	paths := strings.Split(imagePaths, ",")
	urls := make([]string, 0, len(paths))

	for _, path := range paths {
		path = strings.TrimSpace(path)
		if path != "" {
			urls = append(urls, baseURL+path)
		}
	}

	return urls
}

// FormatTimestamp 将时间戳转换为 Y-m-d H:i:s 格式
func FormatTimestamp(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// ParseTimeString 将时间字符串转换为时间戳
func ParseTimeString(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 尝试解析 Y-m-d H:i:s 格式
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		// 尝试解析 Y-m-d 格式
		t, err = time.Parse("2006-01-02", timeStr)
		if err != nil {
			return 0, err
		}
	}

	return t.Unix(), nil
}

// FormatTime 将 time.Time 转换为 Y-m-d H:i:s 格式字符串
func FormatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}
