package function

import (
	"context"
	"errors"
)

// GetAdminUid 从context中获取管理员UID
func GetAdminUid(ctx context.Context) (int64, error) {
	uid, ok := ctx.Value("admin_uid").(int64)
	if !ok {
		return 0, errors.New("admin_uid not found in context")
	}
	return uid, nil
}

// GetAdminVosName 从context中获取管理员VOS名称
func GetAdminVosName(ctx context.Context) (string, error) {
	vosName, ok := ctx.Value("admin_vos_name").(string)
	if !ok {
		return "", errors.New("admin_vos_name not found in context")
	}
	return vosName, nil
}

// GetJwtUid 从context中获取JWT用户UID (用于小程序用户)
func GetJwtUid(ctx context.Context) (int64, error) {
	claims, ok := ctx.Value("jwt_user").(map[string]interface{})
	if !ok {
		return 0, errors.New("jwt_user not found in context")
	}

	uid, ok := claims["uid"].(float64) // JSON数字默认解析为float64
	if !ok {
		return 0, errors.New("uid not found in jwt claims")
	}

	return int64(uid), nil
}
