package model

import (
	"context"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhPrizeCardModel = (*customVhPrizeCardModel)(nil)

type (
	// VhPrizeCardModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhPrizeCardModel.
	VhPrizeCardModel interface {
		vhPrizeCardModel
		BatchInsert(ctx context.Context, data []*VhPrizeCard) error
		CountByBatchId(ctx context.Context, batchId uint64) (int64, error)
	}

	customVhPrizeCardModel struct {
		*defaultVhPrizeCardModel
	}
)

// NewVhPrizeCardModel returns a model for the database table.
func NewVhPrizeCardModel(conn sqlx.SqlConn) VhPrizeCardModel {
	return &customVhPrizeCardModel{
		defaultVhPrizeCardModel: newVhPrizeCardModel(conn),
	}
}

// BatchInsert 批量插入奖品卡
func (m *customVhPrizeCardModel) BatchInsert(ctx context.Context, data []*VhPrizeCard) error {
	if len(data) == 0 {
		return nil
	}

	// 构建批量插入SQL
	valueStrings := make([]string, 0, len(data))
	valueArgs := make([]interface{}, 0, len(data)*6)

	for _, card := range data {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs, card.BatchId, card.No, card.Status, card.Uid, card.RecordId, card.QrcodeUrl)
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
		m.table,
		vhPrizeCardRowsExpectAutoSet,
		strings.Join(valueStrings, ","))

	_, err := m.conn.ExecCtx(ctx, query, valueArgs...)
	return err
}

// CountByBatchId 根据批次ID统计奖品卡数量
func (m *customVhPrizeCardModel) CountByBatchId(ctx context.Context, batchId uint64) (int64, error) {
	query := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE batch_id = ?", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, batchId)
	return count, err
}
