package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhPrizeCardModel = (*customVhPrizeCardModel)(nil)

type (
	// VhPrizeCardModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhPrizeCardModel.
	VhPrizeCardModel interface {
		vhPrizeCardModel
		BatchInsert(ctx context.Context, data []*VhPrizeCard) error
	}

	customVhPrizeCardModel struct {
		*defaultVhPrizeCardModel
	}
)

// NewVhPrizeCardModel returns a model for the database table.
func NewVhPrizeCardModel(conn sqlx.SqlConn) VhPrizeCardModel {
	return &customVhPrizeCardModel{
		defaultVhPrizeCardModel: newVhPrizeCardModel(conn),
	}
}

// BatchInsert 批量插入奖品卡
func (m *customVhPrizeCardModel) BatchInsert(ctx context.Context, datas []*VhPrizeCard) error {
	if len(datas) == 0 {
		return nil
	}

	sq := squirrel.Insert(m.table).Columns(vhPrizeConfRowsExpectAutoSet)
	for _, data := range datas {
		sq = sq.Values(data.BatchId, data.No, data.Status, data.Uid, data.RecordId, data.QrcodeUrl)
	}

	query, values, err := sq.ToSql()
	if err != nil {
		return err
	}

	_, err = m.conn.ExecCtx(ctx, query, values...)
	return err
}
