package model

import (
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	ErrNotFound = sqlx.ErrNotFound
)

type (
	MerchantRedemptionListInfo struct {
		CompanyName string `db:"company_name"` // 公司名称
		VhMerchantRedemption
	}

	UserPrizeRecordListInfo struct {
		Phone       string `db:"phone"`        // 用户手机号
		CompanyName string `db:"company_name"` // 核销商家公司名称
		VhPrizeRecord
	}
)

func CountBuilder(field string, table string) squirrel.SelectBuilder {
	return squirrel.Select("COUNT(" + field + ") as count").From(table)
}

// GetOffset 获取起始数据
func GetOffset(page, pageSize int64) uint64 {
	return uint64((page - 1) * pageSize)
}
