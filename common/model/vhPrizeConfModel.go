package model

import (
	"context"
	"database/sql"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhPrizeConfModel = (*customVhPrizeConfModel)(nil)

type (
	// VhPrizeConfModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhPrizeConfModel.
	VhPrizeConfModel interface {
		vhPrizeConfModel
		Inserts(ctx context.Context, datas []*VhPrizeConf) (sql.Result, error)
	}

	customVhPrizeConfModel struct {
		*defaultVhPrizeConfModel
	}
)

// NewVhPrizeConfModel returns a model for the database table.
func NewVhPrizeConfModel(conn sqlx.SqlConn) VhPrizeConfModel {
	return &customVhPrizeConfModel{
		defaultVhPrizeConfModel: newVhPrizeConfModel(conn),
	}
}

func (m *defaultVhPrizeConfModel) Inserts(ctx context.Context, datas []*VhPrizeConf) (sql.Result, error) {
	if len(datas) == 0 {
		return nil, nil
	}

	sq := squirrel.Insert(m.table).Columns(vhPrizeConfRowsExpectAutoSet)
	for _, data := range datas {
		sq = sq.Values(data.BatchId, data.RegionCode, data.RegionName, data.PrizeName, data.Status, data.Points, data.RedeemQuantity, data.PrizeCt, data.WinCt, data.ValidDays)
	}

	query, values, err := sq.ToSql()
	if err != nil {
		return nil, err
	}

	ret, err := m.conn.ExecCtx(ctx, query, values...)
	if err != nil {
		return nil, err
	}

	return ret, err
}
