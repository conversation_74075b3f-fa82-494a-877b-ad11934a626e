package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhSupplierModel = (*customVhSupplierModel)(nil)

type (
	// VhSupplierModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhSupplierModel.
	VhSupplierModel interface {
		vhSupplierModel
	}

	customVhSupplierModel struct {
		*defaultVhSupplierModel
	}
)

// NewVhSupplierModel returns a model for the database table.
func NewVhSupplierModel(conn sqlx.SqlConn) VhSupplierModel {
	return &customVhSupplierModel{
		defaultVhSupplierModel: newVhSupplierModel(conn),
	}
}
