// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhPrizeCardFieldNames          = builder.RawFieldNames(&VhPrizeCard{})
	vhPrizeCardRows                = strings.Join(vhPrizeCardFieldNames, ",")
	vhPrizeCardRowsExpectAutoSet   = strings.Join(stringx.Remove(vhPrizeCardFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhPrizeCardRowsWithPlaceHolder = strings.Join(stringx.Remove(vhPrizeCardFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhPrizeCardModel interface {
		Insert(ctx context.Context, data *VhPrizeCard) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhPrizeCard) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhPrizeCard, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhPrizeCard, error)
		FindOneByNo(ctx context.Context, no string) (*VhPrizeCard, error)
		Update(ctx context.Context, data *VhPrizeCard) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhPrizeCardModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhPrizeCard struct {
		Id         uint64    `db:"id"`
		BatchId    uint64    `db:"batch_id"`    // 批次id
		No         string    `db:"no"`          // 唯一号(20位)
		CreateTime time.Time `db:"create_time"` // 创建时间
		Status     uint64    `db:"status"`      // 1未使用，2已使用，3已作废
		Uid        uint64    `db:"uid"`         // 使用人，未使用时为0
		RecordId   uint64    `db:"record_id"`   // 中奖记录id,0未使用
		QrcodeUrl  string    `db:"qrcode_url"`  // 二维码地址
	}
)

func newVhPrizeCardModel(conn sqlx.SqlConn) *defaultVhPrizeCardModel {
	return &defaultVhPrizeCardModel{
		conn:  conn,
		table: "`vh_prize_card`",
	}
}

func (m *defaultVhPrizeCardModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhPrizeCardModel) FindOne(ctx context.Context, id uint64) (*VhPrizeCard, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhPrizeCardRows, m.table)
	var resp VhPrizeCard
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeCardModel) FindOneByNo(ctx context.Context, no string) (*VhPrizeCard, error) {
	var resp VhPrizeCard
	query := fmt.Sprintf("select %s from %s where `no` = ? limit 1", vhPrizeCardRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, no)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeCardModel) Insert(ctx context.Context, data *VhPrizeCard) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, vhPrizeCardRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.BatchId, data.No, data.Status, data.Uid, data.RecordId, data.QrcodeUrl)
	return ret, err
}

func (m *defaultVhPrizeCardModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhPrizeCard) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, vhPrizeCardRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.BatchId, data.No, data.Status, data.Uid, data.RecordId, data.QrcodeUrl)
	return ret, err
}

func (m *defaultVhPrizeCardModel) Update(ctx context.Context, newData *VhPrizeCard) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhPrizeCardRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.BatchId, newData.No, newData.Status, newData.Uid, newData.RecordId, newData.QrcodeUrl, newData.Id)
	return err
}

func (m *defaultVhPrizeCardModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhPrizeCardRows).From(m.table)
}

func (m *defaultVhPrizeCardModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhPrizeCard, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhPrizeCard
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeCardModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhPrizeCardModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhPrizeCardModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhPrizeCardModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeCardModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeCardModel) TableName() string {
	return m.table
}
