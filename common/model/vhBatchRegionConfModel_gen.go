// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhBatchRegionConfFieldNames          = builder.RawFieldNames(&VhBatchRegionConf{})
	vhBatchRegionConfRows                = strings.Join(vhBatchRegionConfFieldNames, ",")
	vhBatchRegionConfRowsExpectAutoSet   = strings.Join(stringx.Remove(vhBatchRegionConfFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhBatchRegionConfRowsWithPlaceHolder = strings.Join(stringx.Remove(vhBatchRegionConfFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhBatchRegionConfModel interface {
		Insert(ctx context.Context, data *VhBatchRegionConf) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhBatchRegionConf) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhBatchRegionConf, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhBatchRegionConf, error)
		FindOneByBatchIdRegionCode(ctx context.Context, batchId uint64, regionCode string) (*VhBatchRegionConf, error)
		Update(ctx context.Context, data *VhBatchRegionConf) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhBatchRegionConfModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhBatchRegionConf struct {
		Id         uint64 `db:"id"`
		BatchId    uint64 `db:"batch_id"`    // 批次id
		RegionCode string `db:"region_code"` // 大区code
		RegionName string `db:"region_name"` // 大区名称
		PrizeCt    uint64 `db:"prize_ct"`    // 总中奖瓶数
	}
)

func newVhBatchRegionConfModel(conn sqlx.SqlConn) *defaultVhBatchRegionConfModel {
	return &defaultVhBatchRegionConfModel{
		conn:  conn,
		table: "`vh_batch_region_conf`",
	}
}

func (m *defaultVhBatchRegionConfModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhBatchRegionConfModel) FindOne(ctx context.Context, id uint64) (*VhBatchRegionConf, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhBatchRegionConfRows, m.table)
	var resp VhBatchRegionConf
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBatchRegionConfModel) FindOneByBatchIdRegionCode(ctx context.Context, batchId uint64, regionCode string) (*VhBatchRegionConf, error) {
	var resp VhBatchRegionConf
	query := fmt.Sprintf("select %s from %s where `batch_id` = ? and `region_code` = ? limit 1", vhBatchRegionConfRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, batchId, regionCode)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBatchRegionConfModel) Insert(ctx context.Context, data *VhBatchRegionConf) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, vhBatchRegionConfRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.BatchId, data.RegionCode, data.RegionName, data.PrizeCt)
	return ret, err
}

func (m *defaultVhBatchRegionConfModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhBatchRegionConf) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, vhBatchRegionConfRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.BatchId, data.RegionCode, data.RegionName, data.PrizeCt)
	return ret, err
}

func (m *defaultVhBatchRegionConfModel) Update(ctx context.Context, newData *VhBatchRegionConf) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhBatchRegionConfRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.BatchId, newData.RegionCode, newData.RegionName, newData.PrizeCt, newData.Id)
	return err
}

func (m *defaultVhBatchRegionConfModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhBatchRegionConfRows).From(m.table)
}

func (m *defaultVhBatchRegionConfModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhBatchRegionConf, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhBatchRegionConf
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBatchRegionConfModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhBatchRegionConfModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhBatchRegionConfModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhBatchRegionConfModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhBatchRegionConfModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhBatchRegionConfModel) TableName() string {
	return m.table
}
