// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhSupplierFieldNames          = builder.RawFieldNames(&VhSupplier{})
	vhSupplierRows                = strings.Join(vhSupplierFieldNames, ",")
	vhSupplierRowsExpectAutoSet   = strings.Join(stringx.Remove(vhSupplierFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhSupplierRowsWithPlaceHolder = strings.Join(stringx.Remove(vhSupplierFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhSupplierModel interface {
		Insert(ctx context.Context, data *VhSupplier) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhSupplier) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhSupplier, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhSupplier, error)
		Update(ctx context.Context, data *VhSupplier) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhSupplierModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhSupplier struct {
		Id               int64          `db:"id"`
		SupplierName     string         `db:"supplier_name"`     // 供应商名称
		SupplierTel      sql.NullString `db:"supplier_tel"`      // 供应商电话
		SupplierDirector sql.NullString `db:"supplier_director"` // 供应商负责人
		DeleteTime       int64          `db:"delete_time"`
		RaxNo            string         `db:"rax_no"`           // 纳税号
		Corp             string         `db:"corp"`             // 公司 001 科技 002 云酒
		ContractStart    sql.NullTime   `db:"contract_start"`   // 合同起始日期
		ContractEnd      sql.NullTime   `db:"contract_end"`     // 合同截止日期
		SignType         int64          `db:"sign_type"`        // 1-新签2-续签
		TransferTime     sql.NullTime   `db:"transfer_time"`    // 转移时间
		FirstOrderTime   sql.NullTime   `db:"first_order_time"` // 首次下单时间
		ApprovalId       int64          `db:"approval_id"`      // 操作日志id
		Remark           sql.NullString `db:"remark"`
		CreateTime       time.Time      `db:"create_time"`
		UpdateTime       time.Time      `db:"update_time"`
		SupplierTax      string         `db:"supplier_tax"` // 供应商指定税率
		FromSource       string         `db:"from_source"`
	}
)

func newVhSupplierModel(conn sqlx.SqlConn) *defaultVhSupplierModel {
	return &defaultVhSupplierModel{
		conn:  conn,
		table: "`vh_supplier`",
	}
}

func (m *defaultVhSupplierModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhSupplierModel) FindOne(ctx context.Context, id int64) (*VhSupplier, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhSupplierRows, m.table)
	var resp VhSupplier
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhSupplierModel) Insert(ctx context.Context, data *VhSupplier) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhSupplierRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.SupplierName, data.SupplierTel, data.SupplierDirector, data.DeleteTime, data.RaxNo, data.Corp, data.ContractStart, data.ContractEnd, data.SignType, data.TransferTime, data.FirstOrderTime, data.ApprovalId, data.Remark, data.SupplierTax, data.FromSource)
	return ret, err
}

func (m *defaultVhSupplierModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhSupplier) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhSupplierRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.SupplierName, data.SupplierTel, data.SupplierDirector, data.DeleteTime, data.RaxNo, data.Corp, data.ContractStart, data.ContractEnd, data.SignType, data.TransferTime, data.FirstOrderTime, data.ApprovalId, data.Remark, data.SupplierTax, data.FromSource)
	return ret, err
}

func (m *defaultVhSupplierModel) Update(ctx context.Context, data *VhSupplier) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhSupplierRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.SupplierName, data.SupplierTel, data.SupplierDirector, data.DeleteTime, data.RaxNo, data.Corp, data.ContractStart, data.ContractEnd, data.SignType, data.TransferTime, data.FirstOrderTime, data.ApprovalId, data.Remark, data.SupplierTax, data.FromSource, data.Id)
	return err
}

func (m *defaultVhSupplierModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhSupplierRows).From(m.table)
}

func (m *defaultVhSupplierModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhSupplier, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhSupplier
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhSupplierModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhSupplierModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhSupplierModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhSupplierModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhSupplierModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhSupplierModel) TableName() string {
	return m.table
}
